# 🔮 Agartha: The Lost City of Light - BETA

**An AI-Powered RPG Adventure into the Mystical Underground Realm - No Login Required**

[![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/your-repo/agartha-rpg)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![AI Powered](https://img.shields.io/badge/AI-WebLLM-purple.svg)](https://webllm.mlc.ai/)

## 🌟 Overview

Agartha is a production-ready, AI-powered RPG that runs entirely in your browser. Explore the legendary underground realm beneath Earth's surface, guided by an intelligent AI Dungeon Master that creates dynamic, personalized adventures based on real mythological sources.

### ✨ Key Features

- **🚧 BETA VERSION**: Open access - no login or authentication required!
- **🎭 DEMO MODE**: Test instantly with pre-written responses - no AI download needed!
- **🤖 AI-Powered Storytelling**: Advanced AI Dungeon Master using WebLLM technology
- **🎭 Rich Character System**: 6 unique classes with deep lore and progression
- **🌍 Immersive World**: Based on real mythology (Lemuria, Atlantis, Hollow Earth)
- **💎 Dynamic Gameplay**: Quests, NPCs, inventory, and environmental interactions
- **📱 Cross-Platform**: Runs on any modern browser with WebGPU support
- **💾 Save System**: Multiple save slots with auto-save functionality
- **🎨 Beautiful UI**: Glass morphism design with animations and particle effects
- **🔓 No Barriers**: Jump straight into the adventure - no accounts needed!

## 🚀 Quick Start - BETA ACCESS

### Option 1: Direct Download (Instant Play)
1. Download all files to a folder
2. Open `index.html` in a modern browser
3. Select an AI model and start your adventure immediately!
4. **No login, no signup, no barriers - just pure adventure!**

### Option 2: Web Server
1. Upload files to any web hosting service (GitHub Pages, Netlify, etc.)
2. Share the URL with friends for multiplayer adventures
3. No server setup or API keys required!

## 🎮 Gameplay Features

### Character Classes

| Class | Description | Primary Stat | Special Abilities |
|-------|-------------|--------------|-------------------|
| **Crystal Keeper** | Master of Ancient Energies | Vril | Crystal Resonance, Energy Channeling |
| **Vril Engineer** | Manipulator of Life Force | Resonance | Vril Manipulation, Technology Interface |
| **Lemurian Scholar** | Keeper of Lost Knowledge | Wisdom | Ancient Knowledge, Telepathy |
| **Atlantean Warrior** | Guardian of the Old Ways | Vitality | Combat Mastery, Tactical Awareness |
| **Inner Earth Scout** | Explorer of Hidden Realms | Agility | Tunnel Sense, Stealth Movement |
| **Light Weaver** | Channeler of Sacred Geometries | Resonance | Light Manipulation, Sacred Geometry |

### Game Mechanics

- **📊 Character Progression**: Level up, gain skills, unlock abilities
- **🎲 Dice-Based Actions**: D20 system with critical successes and failures
- **🎒 Inventory Management**: Collect mystical artifacts and ancient tools
- **🗺️ World Exploration**: Discover hidden locations and secret passages
- **👥 NPC Interactions**: Meet beings from lost civilizations
- **📜 Quest System**: Dynamic quests generated by AI

## 🏗️ Technical Architecture

### Modular Design
```
agartha-rpg/
├── index.html              # Main game interface
├── styles/                 # CSS modules
│   ├── main.css           # Core styles and variables
│   ├── components.css     # UI component styles
│   ├── animations.css     # Animation definitions
│   └── game.css          # Game-specific styles
├── js/                    # JavaScript modules
│   ├── main.js           # Application orchestration
│   ├── config.js         # Game configuration
│   ├── lore.js           # World building and mythology
│   ├── game-engine.js    # Core game logic
│   ├── ai-manager.js     # AI integration
│   ├── character-manager.js # Character system
│   ├── ui-manager.js     # User interface
│   └── save-manager.js   # Save/load functionality
└── README.md             # This file
```

### AI Models Supported

| Model | Size | Parameters | Speed | Best For |
|-------|------|------------|-------|----------|
| **Phi-3.5 Mini** ⭐ | 821MB | 3.8B | Fast | Recommended for most users |
| **Llama 3.2 3B** | 1.7GB | 3B | Powerful | Rich storytelling |
| **Gemma 2 2B** | 1.3GB | 2B | Creative | Unique narratives |
| **TinyLlama 1.1B** | 637MB | 1.1B | Ultra-fast | Low-resource devices |

## 🌍 Lore & World Building

### The Three Lost Civilizations

**🌊 Lemuria (Mu)**
- First great civilization of telepathic beings
- Masters of consciousness and crystal technology
- Survivors founded Telos beneath Mt. Shasta

**🏛️ Atlantis**
- Advanced technological civilization
- Developed Vril energy manipulation
- Refugees established underground cities

**🔮 Agartha**
- Underground kingdom uniting all survivors
- Powered by the Central Sun (Smoky God)
- Network of illuminated cities and tunnels

### Key Locations

- **Crystal Gates of Shambhala**: Entrance to the inner realm
- **Shambhala Central Plaza**: Heart of the great underground city
- **Hall of Records**: Ancient library of cosmic knowledge
- **Telos**: Hidden city of the Telosians
- **Vril Power Station**: Energy distribution center

## 🛠️ Development

### Prerequisites
- Modern browser with WebGPU support (Chrome 113+, Edge 113+)
- No server setup required for local development
- Optional: Web server for multiplayer features

### Local Development
```bash
# Clone the repository
git clone https://github.com/your-repo/agartha-rpg.git

# Navigate to directory
cd agartha-rpg

# Open in browser (or use a local server)
open index.html
```

### Configuration
Edit `js/config.js` to customize:
- Game mechanics and balance
- AI model settings
- UI preferences
- Save system options

## 🎯 Deployment

### GitHub Pages
1. Fork this repository
2. Enable GitHub Pages in repository settings
3. Your game will be available at `https://username.github.io/agartha-rpg`

### Netlify
1. Connect your repository to Netlify
2. Deploy with default settings
3. Share your custom domain

### Any Web Host
1. Upload all files to your web hosting service
2. Ensure HTTPS is enabled for WebGPU support
3. Share the URL with players

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Areas for Contribution
- 🎨 Additional character classes and abilities
- 🗺️ New locations and quests
- 🎵 Sound effects and music
- 🌐 Translations and localization
- 🐛 Bug fixes and optimizations

## 📜 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **WebLLM Team**: For making browser-based AI possible
- **Mythological Sources**: Plato's Atlantis, Theosophical texts, Hollow Earth theories
- **Open Source Community**: For the tools and libraries that made this possible

## 📞 Support

- 🐛 **Bug Reports**: [GitHub Issues](https://github.com/your-repo/agartha-rpg/issues)
- 💬 **Discussions**: [GitHub Discussions](https://github.com/your-repo/agartha-rpg/discussions)
- 📧 **Email**: <EMAIL>

---

**Ready to explore the mysteries of the inner Earth? Your adventure awaits in Agartha!** 🔮✨

*"The truth is not for all men, but only for those who seek it."* - Ayn Rand
