<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agartha: Adventure Begins - AI-Powered RPG</title>
    <meta name="description" content="Your adventure in the mystical underground realm of Agartha begins now">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔮</text></svg>">
    
    <!-- CSS Stylesheets -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/game.css">
    <link rel="stylesheet" href="styles/multiplayer.css">
    
    <style>
        /* Game page specific styles */
        .game-page {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .game-header {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 10px 20px;
            position: sticky;
            top: 0;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .game-header h1 {
            color: var(--primary-cyan);
            font-size: 1.2rem;
            margin: 0;
            flex: 1;
            text-align: center;
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: var(--text-light);
            padding: 8px 12px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        .game-content {
            flex: 1;
            display: flex;
            max-height: calc(100vh - 60px);
        }
        
        .game-sidebar {
            width: 300px;
            min-width: 250px;
            background: rgba(0, 0, 0, 0.3);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            overflow-y: auto;
            padding: 20px;
        }
        
        .game-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-width: 0;
        }
        
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .chat-input-area {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
        }
        
        .chat-input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }
        
        .chat-input {
            flex: 1;
            min-height: 44px;
            max-height: 120px;
            resize: none;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            padding: 12px 16px;
            font-size: 0.9rem;
        }
        
        .chat-input:focus {
            outline: none;
            border-color: var(--primary-cyan);
            box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.2);
        }
        
        .send-btn {
            min-width: 60px;
            height: 44px;
            border-radius: 10px;
            background: var(--gradient-primary);
            border: none;
            color: white;
            cursor: pointer;
            font-size: 1.2rem;
        }
        
        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 15px;
            margin-bottom: 10px;
        }
        
        .message.player {
            align-self: flex-end;
            background: var(--gradient-primary);
            color: white;
            border-bottom-right-radius: 5px;
        }
        
        .message.ai {
            align-self: flex-start;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: var(--text-light);
            border-bottom-left-radius: 5px;
        }
        
        .player-info {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .player-name {
            color: var(--primary-cyan);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .player-class {
            color: var(--text-medium);
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .player-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 0.8rem;
        }
        
        .stat {
            display: flex;
            justify-content: space-between;
            color: var(--text-dim);
        }
        
        .inventory {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
        }
        
        .inventory h3 {
            color: var(--primary-gold);
            margin: 0 0 10px 0;
            font-size: 1rem;
        }
        
        .inventory-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: 8px 12px;
            margin-bottom: 5px;
            font-size: 0.85rem;
            color: var(--text-light);
        }
        
        /* Mobile responsive */
        @media (max-width: 768px) {
            .game-content {
                flex-direction: column;
                max-height: none;
            }
            
            .game-sidebar {
                width: 100%;
                min-width: auto;
                max-height: 200px;
                border-right: none;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                padding: 15px;
            }
            
            .player-stats {
                grid-template-columns: repeat(3, 1fr);
                gap: 5px;
                font-size: 0.75rem;
            }
            
            .chat-messages {
                padding: 15px;
            }
            
            .chat-input-area {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="game-page">
        <!-- Game Header -->
        <div class="game-header">
            <a href="index.html" class="back-btn">← Back</a>
            <h1>🔮 Agartha: The Lost City of Light</h1>
            <div id="multiplayerStatus" class="multiplayer-status">
                <div class="multiplayer-inactive">Single Player</div>
            </div>
        </div>
        
        <!-- Game Content -->
        <div class="game-content">
            <!-- Sidebar -->
            <div class="game-sidebar">
                <!-- Player Info -->
                <div class="player-info" id="playerInfo">
                    <div class="player-name" id="playerName">Loading...</div>
                    <div class="player-class" id="playerClass">Preparing your journey...</div>
                    <div class="player-stats" id="playerStats">
                        <!-- Stats will be populated by JavaScript -->
                    </div>
                </div>
                
                <!-- Inventory -->
                <div class="inventory">
                    <h3>🎒 Inventory</h3>
                    <div id="inventoryList">
                        <!-- Inventory items will be populated by JavaScript -->
                    </div>
                </div>
            </div>
            
            <!-- Main Game Area -->
            <div class="game-main">
                <div class="chat-area">
                    <!-- Messages -->
                    <div class="chat-messages" id="gameOutput">
                        <!-- Game messages will appear here -->
                    </div>
                    
                    <!-- Input Area -->
                    <div class="chat-input-area">
                        <div class="chat-input-container">
                            <textarea 
                                id="gameInput" 
                                class="chat-input" 
                                placeholder="What would you like to do? (e.g., 'look around', 'examine the gates', 'talk to someone')"
                                rows="1"
                                disabled
                            ></textarea>
                            <button id="sendBtn" class="send-btn" disabled>
                                🚀
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Game JavaScript -->
    <script src="js/agartha-bundle.js"></script>
    <script>
        // Initialize game page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎮 Game page loaded');
            
            // Get player data from localStorage or URL params
            const playerData = localStorage.getItem('agarthaPlayer');
            const aiModel = localStorage.getItem('agarthaAIModel');
            
            if (!playerData || !aiModel) {
                alert('No character data found. Redirecting to character creation...');
                window.location.href = 'index.html';
                return;
            }
            
            // Initialize the game with stored data
            if (window.agarthaApp) {
                window.agarthaApp.initializeGamePage(JSON.parse(playerData), aiModel);
            } else {
                // Wait for the app to load
                setTimeout(() => {
                    if (window.agarthaApp) {
                        window.agarthaApp.initializeGamePage(JSON.parse(playerData), aiModel);
                    }
                }, 1000);
            }
        });
    </script>
</body>
</html>
