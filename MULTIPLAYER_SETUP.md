# 🎮 Agartha RPG - Multiplayer Setup Guide

## Overview

The Agartha RPG now supports multiplayer functionality where one player hosts the AI model and others join without needing to download anything. This creates a seamless multiplayer experience where:

- **Host**: Downloads and runs the AI model, controls the game world
- **Players**: Join instantly with just a room code, no downloads required
- **Server**: Coordinates game state between all players in real-time

## 🚀 Quick Start

### 1. Set Up the Server

First, you need to run the multiplayer server:

```bash
# Install dependencies
npm install

# Start the server
npm start
```

The server will start on `http://localhost:3000` by default.

### 2. Host a Game

1. Open the game in your browser: `http://localhost:3000`
2. Click "🎮 Multiplayer" button
3. Choose "Host Game"
4. Configure:
   - Server URL: `ws://localhost:3000` (or your server URL)
   - Select an AI model (this will download to your device)
   - Create your character
5. Click "🚀 Start Hosting"
6. Share the room code or URL with friends

### 3. Join a Game

1. Open the game in your browser: `http://localhost:3000`
2. Click "🎮 Multiplayer" button
3. Choose "Join Game"
4. Enter:
   - Server URL: `ws://localhost:3000` (same as host)
   - Room code (get this from the host)
   - Create your character
5. Click "🎮 Join Game"

## 🏗️ Architecture

### How It Works

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Host Player   │    │  Multiplayer    │    │  Other Players  │
│                 │    │     Server      │    │                 │
│ • Runs AI Model │◄──►│                 │◄──►│ • No AI needed  │
│ • Processes     │    │ • Coordinates   │    │ • Instant join  │
│   Actions       │    │   Game State    │    │ • Shared UI     │
│ • Shares State  │    │ • Routes Msgs   │    │ • Real-time     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Components

1. **MultiplayerManager** (`js/multiplayer-manager.js`)
   - Handles WebSocket connections
   - Manages game state synchronization
   - Routes player actions to host

2. **MultiplayerUI** (`js/multiplayer-ui.js`)
   - Provides setup interfaces
   - Manages room creation/joining
   - Updates multiplayer status

3. **Server** (`server.js`)
   - WebSocket server for real-time communication
   - Room management
   - Message routing between players

## 🔧 Configuration

### Server Configuration

Edit `server.js` to customize:

```javascript
const port = process.env.PORT || 3000;  // Server port
const maxInactiveTime = 24 * 60 * 60 * 1000;  // Room cleanup time
```

### Client Configuration

The multiplayer system uses your existing `js/config.js` settings:

```javascript
CONFIG.game.maxPlayers = 12;  // Maximum players per room
CONFIG.network.timeout = 30000;  // Connection timeout
CONFIG.network.heartbeatInterval = 60000;  // Keep-alive interval
```

## 🌐 Deployment Options

### Option 1: Local Network

For playing with friends on the same network:

1. Start server: `npm start`
2. Find your local IP: `ipconfig` (Windows) or `ifconfig` (Mac/Linux)
3. Share URL: `http://YOUR_IP:3000`

### Option 2: Cloud Deployment

#### Heroku
```bash
# Install Heroku CLI
npm install -g heroku

# Create Heroku app
heroku create your-agartha-server

# Deploy
git add .
git commit -m "Deploy multiplayer server"
git push heroku main
```

#### Railway
```bash
# Install Railway CLI
npm install -g @railway/cli

# Deploy
railway login
railway init
railway up
```

#### DigitalOcean App Platform
1. Connect your GitHub repository
2. Set build command: `npm install`
3. Set run command: `npm start`
4. Deploy

### Option 3: VPS/Dedicated Server

For your GoDaddy server or any VPS:

```bash
# Upload files to server
scp -r . <EMAIL>:/path/to/agartha/

# SSH into server
ssh <EMAIL>

# Install Node.js and dependencies
cd /path/to/agartha/
npm install

# Start with PM2 for production
npm install -g pm2
pm2 start server.js --name agartha-multiplayer
pm2 startup
pm2 save
```

## 🔒 Security Considerations

### Basic Security

The current implementation is designed for trusted friends. For public deployment, consider:

1. **Rate Limiting**: Prevent spam/abuse
2. **Authentication**: User accounts and permissions
3. **Room Passwords**: Private room protection
4. **Content Filtering**: Chat moderation
5. **HTTPS/WSS**: Encrypted connections

### Production Hardening

```javascript
// Add to server.js for production
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});

app.use(limiter);
app.use(helmet());
```

## 🐛 Troubleshooting

### Common Issues

**Connection Failed**
- Check server is running: `npm start`
- Verify WebSocket URL format: `ws://` or `wss://`
- Check firewall/port settings

**AI Model Not Loading (Host)**
- Ensure sufficient RAM (4-8GB recommended)
- Check browser WebGPU support
- Try smaller model first

**Players Can't Join**
- Verify room code is correct (case-sensitive)
- Check server URL matches host's server
- Ensure server is accessible from player's network

**Game State Desync**
- Host should refresh and restart room
- Players rejoin with same room code
- Check network stability

### Debug Mode

Enable debug logging:

```javascript
// In js/config.js
CONFIG.game.debugMode = true;
```

This will show detailed console logs for troubleshooting.

## 📊 Performance Tips

### For Hosts
- Use Chrome/Edge with WebGPU enabled
- 8GB+ RAM recommended for larger AI models
- Stable internet connection required
- Keep browser tab active during game

### For Players
- Any modern browser works
- Minimal system requirements
- Only needs stable internet connection

### Server Optimization
- Use SSD storage for better I/O
- 1GB+ RAM for server
- CDN for static file serving
- Load balancer for multiple game rooms

## 🔄 Updates and Maintenance

### Updating the Game
1. Pull latest changes: `git pull`
2. Update dependencies: `npm install`
3. Restart server: `pm2 restart agartha-multiplayer`

### Monitoring
- Check server logs: `pm2 logs agartha-multiplayer`
- Monitor room activity in console
- Track player connections and disconnections

## 🎯 Future Enhancements

Planned features for future versions:

- **Persistent Rooms**: Save game state between sessions
- **Spectator Mode**: Watch games without participating
- **Voice Chat**: Integrated voice communication
- **Advanced Moderation**: Admin controls and chat filtering
- **Mobile App**: Native mobile client
- **AI Model Sharing**: Distribute AI processing across multiple hosts

## 📞 Support

If you encounter issues:

1. Check this guide first
2. Enable debug mode and check console logs
3. Verify all components are up to date
4. Test with a simple local setup first

For technical support, include:
- Browser and version
- Server logs
- Console error messages
- Network configuration details

---

**Happy adventuring in multiplayer Agartha! 🔮✨**
