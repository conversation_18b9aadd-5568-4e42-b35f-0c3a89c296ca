/* ===== AGARTHA RPG - COMPONENT STYLES ===== */

/* ===== BUTTONS ===== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-soft);
    position: relative;
    overflow: hidden;
    min-height: 44px;
    white-space: nowrap;
    touch-action: manipulation;
    width: 100%;
}

/* Mobile button adjustments */
@media (max-width: 767px) {
    .btn {
        padding: 14px 20px;
        font-size: 0.9rem;
        min-height: 48px;
    }
}

/* Tablet and up */
@media (min-width: 768px) {
    .btn {
        width: auto;
        padding: 16px 32px;
        font-size: 1rem;
        min-height: 48px;
    }
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    border: var(--border-glass);
    color: var(--text-light);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-green) 0%, #00cc66 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-amber) 0%, #ff8800 100%);
}

.btn-error {
    background: linear-gradient(135deg, var(--error-red) 0%, #cc2222 100%);
}

/* ===== FORM ELEMENTS ===== */

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--text-light);
    font-weight: 500;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: var(--border-glass);
    border-radius: 10px;
    color: var(--text-light);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    min-height: 44px;
    -webkit-appearance: none;
    appearance: none;
}

/* Mobile form adjustments */
@media (max-width: 767px) {
    .form-input,
    .form-select,
    .form-textarea {
        padding: 14px 16px;
        font-size: 16px; /* Prevents zoom on iOS */
        min-height: 48px;
    }
}

/* Tablet and up */
@media (min-width: 768px) {
    .form-input,
    .form-select,
    .form-textarea {
        padding: 16px 20px;
        font-size: 1rem;
    }
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-cyan);
    box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.2);
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: var(--text-dim);
}

.form-select {
    cursor: pointer;
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

/* ===== CARDS ===== */

.card {
    background: var(--gradient-card);
    border: var(--border-glass);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-medium);
    transition: all 0.3s ease;
    width: 100%;
    max-width: 100%;
}

/* Mobile card adjustments */
@media (max-width: 767px) {
    .card {
        padding: 15px;
        border-radius: 12px;
        margin: 0;
    }
}

/* Tablet and up */
@media (min-width: 768px) {
    .card {
        padding: 30px;
        border-radius: 20px;
        max-width: 600px;
    }
}

/* Desktop */
@media (min-width: 1024px) {
    .card {
        padding: 40px;
        max-width: 700px;
    }
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-strong);
}

.card-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-title {
    color: var(--primary-cyan);
    font-size: 1.5rem;
    margin-bottom: var(--spacing-sm);
}

.card-subtitle {
    color: var(--text-dim);
    font-style: italic;
}

/* ===== PANELS ===== */

.panel {
    background: rgba(255, 255, 255, 0.05);
    border: var(--border-glass);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    backdrop-filter: blur(5px);
}

.panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-title {
    color: var(--primary-cyan);
    font-size: 1.2rem;
    font-weight: 600;
}

/* ===== PROGRESS BARS ===== */

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 4px;
    transition: width var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.progress-fill::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

.stat-bar {
    width: 100%;
    height: 12px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    overflow: hidden;
    margin-top: var(--spacing-xs);
}

.stat-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 6px;
    transition: width var(--transition-medium);
}

/* ===== STATUS INDICATORS ===== */

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: var(--spacing-sm);
    position: relative;
}

.status-indicator.online {
    background: var(--success-green);
    box-shadow: 0 0 8px rgba(0, 255, 136, 0.5);
}

.status-indicator.away {
    background: var(--warning-amber);
    box-shadow: 0 0 8px rgba(255, 170, 0, 0.5);
}

.status-indicator.offline {
    background: var(--text-dim);
}

.status-indicator.loading {
    background: var(--primary-cyan);
    animation: pulse 1.5s ease-in-out infinite;
}

.status-indicator.error {
    background: var(--error-red);
    box-shadow: 0 0 8px rgba(255, 68, 68, 0.5);
}

/* ===== BADGES ===== */

.badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    background: var(--primary-cyan);
    color: var(--bg-dark);
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

/* Mobile badge adjustments */
@media (max-width: 767px) {
    .badge {
        padding: 3px 6px;
        font-size: 0.65rem;
        border-radius: 6px;
    }
}

/* Tablet and up */
@media (min-width: 768px) {
    .badge {
        padding: 4px 10px;
        font-size: 0.75rem;
        border-radius: 10px;
    }
}

.badge-secondary {
    background: var(--text-dim);
    color: var(--text-light);
}

.badge-success {
    background: var(--success-green);
    color: var(--bg-dark);
}

.badge-warning {
    background: var(--warning-amber);
    color: var(--bg-dark);
}

.badge-error {
    background: var(--error-red);
    color: white;
}

/* ===== TOOLTIPS ===== */

.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-dark);
    color: var(--text-light);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    z-index: 1000;
    border: var(--border-glass);
    box-shadow: var(--shadow-medium);
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--bg-dark);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
}

.tooltip:hover::before,
.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* ===== SCROLLBARS ===== */

::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-cyan);
    border-radius: 4px;
    transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-gold);
}

/* Firefox scrollbar */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-cyan) rgba(255, 255, 255, 0.1);
}

/* ===== RESPONSIVE COMPONENTS ===== */

@media (max-width: 768px) {
    .btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: 0.9rem;
    }
    
    .card {
        padding: var(--spacing-lg);
    }
    
    .panel {
        padding: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.85rem;
    }
    
    .card {
        padding: var(--spacing-md);
    }
    
    .panel {
        padding: var(--spacing-sm);
    }
}
