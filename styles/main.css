/* ===== AGARTHA RPG - MAIN STYLES ===== */

/* CSS Variables */
:root {
    /* Primary Colors */
    --primary-cyan: #00ffff;
    --primary-purple: #8a2be2;
    --primary-gold: #ffd700;
    --primary-emerald: #50c878;
    
    /* Background Colors */
    --bg-dark: #0a0015;
    --bg-medium: #1a0033;
    --bg-light: #2a0055;
    --bg-overlay: rgba(0, 0, 0, 0.85);
    
    /* Text Colors */
    --text-light: #e8e8e8;
    --text-medium: #c0c0c0;
    --text-dim: #888888;
    --text-accent: var(--primary-cyan);
    
    /* Status Colors */
    --success-green: #00ff88;
    --warning-amber: #ffaa00;
    --error-red: #ff4444;
    --info-blue: #4488ff;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-cyan) 100%);
    --gradient-background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-medium) 50%, var(--bg-dark) 100%);
    --gradient-card: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    
    /* Shadows */
    --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 8px 32px rgba(0, 0, 0, 0.4);
    --shadow-strong: 0 16px 64px rgba(0, 0, 0, 0.6);
    --shadow-glow: 0 0 20px rgba(0, 255, 255, 0.3);
    
    /* Borders */
    --border-radius: 12px;
    --border-radius-large: 20px;
    --border-glass: 1px solid rgba(255, 255, 255, 0.2);
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
    
    /* Typography */
    --font-family-primary: 'Segoe UI', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-family-mono: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ===== RESET & BASE STYLES ===== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}



html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    background: var(--gradient-background);
    margin: 0;
    padding: 0;
    color: var(--text-light);
    line-height: 1.6;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
}

/* Animated background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 50%, rgba(138, 43, 226, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 191, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 20%, rgba(255, 215, 0, 0.08) 0%, transparent 50%);
    pointer-events: none;
    animation: float 30s ease-in-out infinite;
    z-index: -1;
}

/* ===== TYPOGRAPHY ===== */

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 {
    font-size: 2.5rem;
    color: var(--text-accent);
    text-shadow: var(--shadow-glow);
}

h2 {
    font-size: 2rem;
    color: var(--text-light);
}

h3 {
    font-size: 1.5rem;
    color: var(--text-medium);
}

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-medium);
}

a {
    color: var(--primary-cyan);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-gold);
}

/* ===== UTILITY CLASSES ===== */

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-cyan); }
.text-secondary { color: var(--primary-purple); }
.text-success { color: var(--success-green); }
.text-warning { color: var(--warning-amber); }
.text-error { color: var(--error-red); }

.hidden { display: none !important; }
.visible { display: block !important; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { justify-content: center; align-items: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.m-0 { margin: 0; }
.p-0 { padding: 0; }

/* Spacing utilities */
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }

.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

/* ===== GLASS MORPHISM EFFECTS ===== */

.glass {
    background: var(--gradient-card);
    border: var(--border-glass);
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-medium);
}

.glass-strong {
    background: rgba(255, 255, 255, 0.15);
    border: var(--border-glass);
    border-radius: var(--border-radius-large);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-strong);
}

/* ===== LOADING OVERLAY ===== */

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-overlay);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 3000;
    backdrop-filter: blur(5px);
}

.loading-overlay.active {
    display: flex;
}

.loading-content {
    text-align: center;
    padding: var(--spacing-xl);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(0, 255, 255, 0.3);
    border-top-color: var(--primary-cyan);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg);
}

.loading-text {
    color: var(--primary-cyan);
    font-size: 1.2em;
    font-weight: 500;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
    :root {
        --spacing-md: 12px;
        --spacing-lg: 18px;
        --spacing-xl: 24px;
    }
    
    h1 { font-size: 2rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
}

@media (max-width: 480px) {
    :root {
        --spacing-md: 8px;
        --spacing-lg: 12px;
        --spacing-xl: 16px;
    }
    
    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.25rem; }
    h3 { font-size: 1.1rem; }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for keyboard navigation */
*:focus {
    outline: 2px solid var(--primary-cyan);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --text-light: #ffffff;
        --text-medium: #ffffff;
        --bg-dark: #000000;
        --bg-medium: #000000;
    }
}
