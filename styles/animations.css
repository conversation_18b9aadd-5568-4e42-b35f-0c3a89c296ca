/* ===== AGARTHA RPG - ANIMATIONS ===== */

/* ===== KEYFRAME ANIMATIONS ===== */

@keyframes float {
    0%, 100% { 
        transform: translateY(0) rotate(0deg); 
    }
    33% { 
        transform: translateY(-20px) rotate(1deg); 
    }
    66% { 
        transform: translateY(20px) rotate(-1deg); 
    }
}

@keyframes spin {
    from { 
        transform: rotate(0deg); 
    }
    to { 
        transform: rotate(360deg); 
    }
}

@keyframes pulse {
    0%, 100% { 
        opacity: 1; 
        transform: scale(1); 
    }
    50% { 
        opacity: 0.7; 
        transform: scale(1.1); 
    }
}

@keyframes glow {
    0%, 100% { 
        text-shadow: 0 0 20px rgba(0, 255, 255, 0.5); 
    }
    50% { 
        text-shadow: 0 0 30px rgba(0, 255, 255, 0.8), 0 0 40px rgba(0, 255, 255, 0.4); 
    }
}

@keyframes shimmer {
    0% { 
        left: -100%; 
    }
    100% { 
        left: 100%; 
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes scaleOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes crystalGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
    }
    50% {
        box-shadow: 0 0 40px rgba(0, 255, 255, 0.6), 0 0 60px rgba(138, 43, 226, 0.4);
    }
}

@keyframes energyFlow {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* ===== ANIMATION UTILITY CLASSES ===== */

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animate-glow {
    animation: glow 3s ease-in-out infinite;
}

.animate-shimmer {
    animation: shimmer 2s infinite;
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-crystal-glow {
    animation: crystalGlow 4s ease-in-out infinite;
}

.animate-energy-flow {
    background: linear-gradient(-45deg, var(--primary-cyan), var(--primary-purple), var(--primary-gold), var(--primary-emerald));
    background-size: 400% 400%;
    animation: energyFlow 3s ease infinite;
}

/* ===== ENTRANCE ANIMATIONS ===== */

.slide-in-up {
    animation: slideInUp 0.5s ease-out;
}

.slide-in-down {
    animation: slideInDown 0.5s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.scale-in {
    animation: scaleIn 0.3s ease-out;
}

/* ===== EXIT ANIMATIONS ===== */

.fade-out {
    animation: fadeOut 0.3s ease-out forwards;
}

.scale-out {
    animation: scaleOut 0.3s ease-out forwards;
}

/* ===== HOVER ANIMATIONS ===== */

.hover-lift {
    transition: transform var(--transition-medium);
}

.hover-lift:hover {
    transform: translateY(-4px);
}

.hover-glow {
    transition: box-shadow var(--transition-medium);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.4);
}

.hover-scale {
    transition: transform var(--transition-medium);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform var(--transition-medium);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* ===== LOADING ANIMATIONS ===== */

.loading-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
    0%, 20% {
        content: '';
    }
    40% {
        content: '.';
    }
    60% {
        content: '..';
    }
    80%, 100% {
        content: '...';
    }
}

/* AI Thinking Spinner */
.ai-thinking-spinner {
    display: inline-flex;
    align-items: center;
    padding: 12px 16px;
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 8px;
    color: var(--primary-cyan);
    font-size: 16px;
    margin: 8px 0;
}

.typing-dots {
    animation: loadingDots 1.5s infinite;
}

.typing-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
}

.loading-bar {
    position: relative;
    overflow: hidden;
}

.loading-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loadingBar 1.5s infinite;
}

@keyframes loadingBar {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* ===== TYPING EFFECT ===== */

.typing-effect {
    overflow: hidden;
    border-right: 2px solid var(--primary-cyan);
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink 0.75s step-end infinite;
}

/* ===== PARTICLE EFFECTS ===== */

.particle-container {
    position: relative;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-cyan);
    border-radius: 50%;
    pointer-events: none;
    animation: particleFloat 6s linear infinite;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) scale(1);
        opacity: 0;
    }
}

/* ===== RESPONSIVE ANIMATIONS ===== */

@media (max-width: 768px) {
    .animate-float {
        animation-duration: 8s;
    }
    
    .hover-lift:hover {
        transform: translateY(-2px);
    }
    
    .hover-scale:hover {
        transform: scale(1.02);
    }
}

/* ===== REDUCED MOTION SUPPORT ===== */

@media (prefers-reduced-motion: reduce) {
    .animate-float,
    .animate-spin,
    .animate-pulse,
    .animate-glow,
    .animate-shimmer,
    .animate-bounce,
    .animate-shake,
    .animate-crystal-glow,
    .animate-energy-flow {
        animation: none;
    }
    
    .hover-lift:hover,
    .hover-scale:hover,
    .hover-rotate:hover {
        transform: none;
    }
}
