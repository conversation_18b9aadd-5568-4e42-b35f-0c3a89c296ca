/* ===== AGARTHA RPG - GAME SPECIFIC STYLES ===== */

/* ===== LAYOUT CONTAINERS ===== */

/* ===== MOBILE-FIRST RESPONSIVE LAYOUT ===== */

.model-setup,
.character-creation {
    min-height: 100vh;
    background: radial-gradient(ellipse at center, rgba(138, 43, 226, 0.2) 0%, rgba(0, 0, 0, 0.9) 100%);
    padding: 10px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.model-setup .card,
.character-creation .card {
    width: 100%;
    max-width: none;
    margin: 0;
    padding: 15px;
    border-radius: 15px;
}

/* Tablet and up */
@media (min-width: 768px) {
    .model-setup,
    .character-creation {
        padding: 20px;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        padding-top: 40px;
    }

    .model-setup .card,
    .character-creation .card {
        max-width: 700px;
        padding: 30px;
    }
}

/* Desktop */
@media (min-width: 1024px) {
    .model-setup .card,
    .character-creation .card {
        max-width: 800px;
        padding: 40px;
    }
}

.model-setup.hidden,
.character-creation.hidden {
    display: none;
}

.character-creation.active {
    display: flex;
}

.game-container {
    display: none;
    min-height: 100vh;
    background: var(--gradient-background);
}

.game-container.active {
    display: flex;
}

.game-sidebar {
    width: 320px;
    min-width: 280px;
    max-width: 400px;
    background: rgba(0, 0, 0, 0.3);
    border-right: var(--border-glass);
    backdrop-filter: blur(10px);
    overflow-y: auto;
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    position: relative;
}

/* ===== MODEL SELECTION ===== */

.model-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin: 15px 0;
}

/* Mobile model options */
@media (max-width: 767px) {
    .model-options {
        gap: 10px;
        margin: 10px 0;
    }
}

.model-option {
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    touch-action: manipulation;
}

/* Mobile model options */
@media (max-width: 767px) {
    .model-option {
        padding: 12px;
        border-radius: 10px;
    }

    .model-option:hover {
        transform: none; /* Disable hover transform on mobile */
    }

    .model-option:active {
        transform: scale(0.98);
        background: rgba(0, 255, 255, 0.15);
    }
}

.model-option:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: var(--primary-cyan);
    transform: translateX(5px);
}

.model-option.selected {
    background: rgba(0, 255, 255, 0.2);
    border-color: var(--primary-cyan);
    box-shadow: var(--shadow-glow);
}

.model-option.selected::after {
    content: '✓';
    position: absolute;
    right: 15px;
    top: 15px;
    color: var(--success-green);
    font-size: 1.5rem;
    font-weight: bold;
}

/* Model card content */
.model-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.model-header h3 {
    color: var(--primary-cyan);
    margin: 0;
    font-size: 1.1rem;
    flex: 1;
    min-width: 0;
}

.model-specs {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

.model-description {
    color: var(--text-medium);
    font-size: 0.9rem;
    margin: 8px 0;
    line-height: 1.4;
}

.model-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-top: 10px;
    font-size: 0.85rem;
}

.model-stats .stat {
    display: flex;
    justify-content: space-between;
    color: var(--text-dim);
}

.stat-label {
    color: var(--text-medium);
}

.stat-value {
    color: var(--primary-cyan);
    font-weight: 600;
}

/* Mobile model card adjustments */
@media (max-width: 767px) {
    .model-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .model-header h3 {
        font-size: 1rem;
    }

    .model-description {
        font-size: 0.85rem;
    }

    .model-stats {
        grid-template-columns: 1fr;
        gap: 6px;
        font-size: 0.8rem;
    }

    .model-option.selected::after {
        right: 12px;
        top: 12px;
        font-size: 1.2rem;
    }
}

.model-option h4 {
    color: var(--primary-cyan);
    margin-bottom: var(--spacing-sm);
    font-size: 1.1rem;
}

.model-option p {
    color: var(--text-dim);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-sm);
}

.model-specs {
    display: flex;
    gap: var(--spacing-md);
    font-size: 0.85rem;
    color: var(--warning-amber);
    flex-wrap: wrap;
}

/* ===== CHARACTER CREATION ===== */

.character-form {
    max-width: 600px;
    margin: 0 auto;
}

.class-description {
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius);
    margin-top: var(--spacing-sm);
    transition: all var(--transition-medium);
}

.class-description.active {
    border-color: var(--primary-cyan);
    background: rgba(0, 255, 255, 0.1);
}

.character-preview {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    border: var(--border-glass);
}

.preview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.preview-stat {
    text-align: center;
    padding: var(--spacing-sm);
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius);
}

.preview-stat-label {
    display: block;
    font-size: 0.8rem;
    color: var(--text-dim);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.preview-stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-cyan);
    margin-top: var(--spacing-xs);
}

/* ===== CHAT INTERFACE ===== */

.chat-header {
    padding: var(--spacing-lg);
    border-bottom: var(--border-glass);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.header-content {
    flex: 1;
    min-width: 0;
}

.game-title {
    font-size: 1.8rem;
    margin-bottom: var(--spacing-sm);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.location-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.location-icon {
    font-size: 1.2rem;
}

.location-name {
    font-weight: 600;
    color: var(--primary-cyan);
    font-size: 1.1rem;
}

.location-details {
    display: flex;
    gap: var(--spacing-sm);
    margin-left: var(--spacing-sm);
}

.game-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.message {
    max-width: 85%;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-large);
    backdrop-filter: blur(5px);
    position: relative;
    animation: slideInUp 0.3s ease-out;
}

.message.player {
    align-self: flex-end;
    background: var(--gradient-primary);
    color: white;
    border-bottom-right-radius: var(--spacing-sm);
}

.message.dm {
    align-self: flex-start;
    background: rgba(255, 255, 255, 0.1);
    border: var(--border-glass);
    border-bottom-left-radius: var(--spacing-sm);
}

.message.system {
    align-self: center;
    background: rgba(255, 215, 0, 0.2);
    border: 1px solid rgba(255, 215, 0, 0.3);
    text-align: center;
    font-style: italic;
    max-width: 70%;
}

.message-author {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.message-timestamp {
    font-size: 0.75rem;
    opacity: 0.7;
    font-weight: normal;
}

.message-content {
    line-height: 1.6;
}

.welcome-message {
    margin: var(--spacing-xl) auto;
    max-width: 600px;
}

/* ===== CHAT INPUT ===== */

.chat-input {
    padding: var(--spacing-lg);
    border-top: var(--border-glass);
    backdrop-filter: blur(10px);
    background: rgba(0, 0, 0, 0.2);
}

.quick-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
    justify-content: center;
}

.quick-action {
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border: var(--border-glass);
    border-radius: var(--border-radius);
    color: var(--text-light);
    font-size: 0.85rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.quick-action:hover {
    background: rgba(0, 255, 255, 0.2);
    border-color: var(--primary-cyan);
    transform: translateY(-2px);
}

.input-container {
    position: relative;
}

.input-wrapper {
    display: flex;
    gap: var(--spacing-sm);
    align-items: flex-end;
}

.input-wrapper .form-input {
    flex: 1;
    min-height: 48px;
    resize: none;
    padding-right: 120px;
}

.input-actions {
    display: flex;
    gap: var(--spacing-xs);
    position: absolute;
    right: var(--spacing-sm);
    bottom: var(--spacing-sm);
}

.input-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-sm);
    font-size: 0.8rem;
    color: var(--text-dim);
}

.character-count {
    opacity: 0.7;
}

.typing-indicator {
    color: var(--primary-cyan);
    font-style: italic;
}

/* ===== CHARACTER STATS ===== */

.character-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.character-header {
    text-align: center;
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.character-name {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--primary-cyan);
    margin-bottom: var(--spacing-xs);
}

.character-class {
    font-size: 0.9rem;
    color: var(--text-dim);
    font-style: italic;
}

.character-level {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    font-size: 0.85rem;
}

.health-bar,
.vril-bar {
    margin-bottom: var(--spacing-md);
}

.bar-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-xs);
    font-size: 0.9rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius);
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-dim);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-weight: bold;
    color: var(--primary-cyan);
}

/* ===== INVENTORY ===== */

.inventory-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-sm);
}

.inventory-slot {
    aspect-ratio: 1;
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.inventory-slot:hover {
    border-color: var(--primary-cyan);
    background: rgba(0, 255, 255, 0.1);
}

.inventory-slot.filled {
    background: rgba(0, 255, 255, 0.2);
    border-color: var(--primary-cyan);
    animation: crystalGlow 3s ease-in-out infinite;
}

.inventory-slot.empty {
    color: var(--text-dim);
    font-size: 1rem;
}

/* ===== QUEST LIST ===== */

.quest-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-height: 300px;
    overflow-y: auto;
}

.quest-item {
    background: rgba(255, 255, 255, 0.05);
    border: var(--border-glass);
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
}

.quest-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-cyan);
}

.quest-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.quest-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
}

.quest-description {
    font-size: 0.85rem;
    line-height: 1.4;
    margin: 0;
}

.quest-progress {
    margin: var(--spacing-sm) 0;
}

.quest-objectives {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.objective {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.85rem;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
}

.objective.completed {
    background: rgba(0, 255, 136, 0.1);
    border-left: 2px solid var(--success-green);
}

.objective.active {
    background: rgba(255, 255, 255, 0.05);
    border-left: 2px solid var(--warning-amber);
}

.objective-icon {
    font-size: 0.9rem;
    flex-shrink: 0;
}

.objective-text {
    flex: 1;
    line-height: 1.3;
}

.objective-text.text-success {
    text-decoration: line-through;
    opacity: 0.8;
}

/* ===== PLAYER LIST ===== */

.player-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.player-item {
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border: var(--border-glass);
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
}

.player-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.player-item.current-player {
    border-color: var(--primary-cyan);
    background: rgba(0, 255, 255, 0.1);
}

.player-name {
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: var(--spacing-xs);
}

.player-class {
    font-size: 0.8rem;
    color: var(--text-dim);
    margin-bottom: var(--spacing-xs);
}

.player-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.8rem;
}

/* ===== MODAL ===== */

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 4000;
    backdrop-filter: blur(5px);
}

.modal-overlay.active {
    display: flex;
}

.modal {
    max-width: 90vw;
    max-height: 90vh;
    width: 600px;
    overflow: hidden;
    animation: scaleIn 0.3s ease-out;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: var(--border-glass);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--error-red);
}

.modal-content {
    padding: var(--spacing-lg);
    overflow-y: auto;
    max-height: 70vh;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 1024px) {
    .game-sidebar {
        width: 280px;
    }
    
    .chat-header {
        padding: var(--spacing-md);
    }
    
    .game-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {
    .game-container {
        flex-direction: column;
    }
    
    .game-sidebar {
        width: 100%;
        height: auto;
        max-height: 40vh;
        border-right: none;
        border-bottom: var(--border-glass);
        flex-direction: row;
        overflow-x: auto;
        padding: var(--spacing-sm);
    }
    
    .game-sidebar .panel {
        min-width: 250px;
        flex-shrink: 0;
    }
    
    .chat-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .location-info {
        width: 100%;
        justify-content: space-between;
    }
    
    .quick-actions {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: var(--spacing-sm);
    }
    
    .message {
        max-width: 95%;
    }
}

@media (max-width: 480px) {
    .model-setup,
    .character-creation {
        padding: var(--spacing-md);
    }
    
    .card {
        padding: var(--spacing-lg);
    }
    
    .chat-messages {
        padding: var(--spacing-md);
    }
    
    .chat-input {
        padding: var(--spacing-md);
    }
    
    .input-wrapper {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .input-actions {
        position: static;
        justify-content: flex-end;
    }
    
    .input-wrapper .form-input {
        padding-right: var(--spacing-md);
    }
}
