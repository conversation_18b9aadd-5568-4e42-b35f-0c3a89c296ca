/**
 * AGARTHA RPG - MULTIPLAYER STYLES
 * CSS styles for multiplayer UI components
 */

/* Multiplayer Modal Styles */
.multiplayer-modal {
    max-width: 800px;
    width: 90vw;
}

.multiplayer-setup {
    padding: 20px;
}

.multiplayer-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin: 20px 0;
}

.option-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.option-card:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--primary-cyan);
    transform: translateY(-2px);
}

.option-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.option-card h4 {
    color: var(--primary-cyan);
    margin: 0 0 10px 0;
    font-size: 1.2rem;
}

.option-card p {
    color: var(--text-light);
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.option-card ul {
    text-align: left;
    margin: 15px 0;
    padding-left: 20px;
}

.option-card li {
    color: var(--text-medium);
    font-size: 0.85rem;
    margin-bottom: 5px;
}

.multiplayer-info {
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
}

.multiplayer-info h4 {
    color: var(--primary-cyan);
    margin: 0 0 10px 0;
}

.multiplayer-info p {
    color: var(--text-light);
    margin: 5px 0;
    font-size: 0.9rem;
}

/* Setup Forms */
.setup-form {
    margin: 20px 0;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: var(--primary-gold);
    font-weight: 600;
    margin-bottom: 5px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    font-size: 0.9rem;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-cyan);
    box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.2);
}

.form-group small {
    display: block;
    color: var(--text-dim);
    font-size: 0.8rem;
    margin-top: 5px;
}

/* Model Selection */
.model-option {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-bottom: 10px;
    overflow: hidden;
}

.model-option input[type="radio"] {
    display: none;
}

.model-option label {
    display: block;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.model-option input[type="radio"]:checked + label {
    background: rgba(0, 255, 255, 0.1);
    border-color: var(--primary-cyan);
}

.model-option label strong {
    color: var(--primary-cyan);
    display: block;
    margin-bottom: 5px;
}

.model-size {
    color: var(--primary-gold);
    font-size: 0.8rem;
    float: right;
}

.model-description {
    color: var(--text-medium);
    font-size: 0.85rem;
    margin-top: 5px;
}

/* Setup Actions */
.setup-actions {
    display: flex;
    gap: 15px;
    justify-content: space-between;
    margin: 30px 0 20px 0;
}

.setup-actions button {
    flex: 1;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

/* Host/Join Info Boxes */
.host-info,
.join-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
}

.host-info h4,
.join-info h4 {
    color: var(--primary-gold);
    margin: 0 0 10px 0;
}

.host-info ul,
.join-info ul {
    margin: 0;
    padding-left: 20px;
}

.host-info li,
.join-info li {
    color: var(--text-light);
    font-size: 0.85rem;
    margin-bottom: 5px;
}

/* Success Modal */
.host-success {
    text-align: center;
    padding: 20px;
}

.room-info {
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.info-item label {
    color: var(--primary-gold);
    font-weight: 600;
    min-width: 100px;
}

.room-code,
.share-url {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 6px;
    font-family: monospace;
    font-size: 0.9rem;
    color: var(--primary-cyan);
}

.copy-btn {
    background: var(--primary-gold);
    color: black;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    background: var(--primary-cyan);
    transform: scale(1.05);
}

.next-steps {
    text-align: left;
    margin: 20px 0;
}

.next-steps ol {
    padding-left: 20px;
}

.next-steps li {
    color: var(--text-light);
    margin-bottom: 8px;
}

/* In-Game Multiplayer UI */
.multiplayer-panel {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
}

.room-info-panel {
    margin-bottom: 15px;
}

.room-info h4 {
    color: var(--primary-cyan);
    margin: 0 0 10px 0;
    font-size: 1rem;
}

.room-info p {
    color: var(--text-light);
    margin: 5px 0;
    font-size: 0.85rem;
}

.players-list-panel h4 {
    color: var(--primary-gold);
    margin: 0 0 10px 0;
    font-size: 1rem;
}

.player-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 5px;
    font-size: 0.85rem;
    color: var(--text-light);
}

.player-item.current-player {
    background: rgba(0, 255, 255, 0.2);
    color: var(--primary-cyan);
}

/* Multiplayer Status */
.multiplayer-status {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.multiplayer-active {
    background: rgba(0, 255, 0, 0.2);
    border: 1px solid rgba(0, 255, 0, 0.5);
    color: #00ff00;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
}

.multiplayer-inactive {
    background: rgba(128, 128, 128, 0.2);
    border: 1px solid rgba(128, 128, 128, 0.5);
    color: #888;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
}

.room-code {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 8px;
    font-family: monospace;
}

/* Multiplayer Button */
.multiplayer-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 10px 0;
}

.multiplayer-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .multiplayer-options {
        grid-template-columns: 1fr;
    }
    
    .setup-actions {
        flex-direction: column;
    }
    
    .info-item {
        flex-direction: column;
        align-items: stretch;
        gap: 5px;
    }
    
    .info-item label {
        min-width: auto;
    }
    
    .multiplayer-status {
        position: relative;
        top: auto;
        right: auto;
        margin: 10px 0;
    }
}
