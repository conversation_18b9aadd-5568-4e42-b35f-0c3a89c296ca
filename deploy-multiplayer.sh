#!/bin/bash

# Agartha RPG - Multiplayer Deployment Script
# This script helps deploy the multiplayer server to various platforms

set -e

echo "🚀 Agartha RPG Multiplayer Deployment Script"
echo "============================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js and npm are installed"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file..."
    cat > .env << EOL
# Agartha RPG Multiplayer Server Configuration
PORT=3000
NODE_ENV=production

# Optional: Add your custom settings here
# MAX_ROOMS=100
# ROOM_CLEANUP_INTERVAL=3600000
# MAX_PLAYERS_PER_ROOM=12
EOL
    echo "✅ .env file created with default settings"
fi

# Function to deploy locally
deploy_local() {
    echo "🏠 Starting local deployment..."
    echo "Server will be available at: http://localhost:3000"
    echo "WebSocket endpoint: ws://localhost:3000/ws"
    echo ""
    echo "To stop the server, press Ctrl+C"
    echo ""
    npm start
}

# Function to deploy with PM2
deploy_pm2() {
    echo "🔄 Deploying with PM2..."
    
    # Install PM2 if not installed
    if ! command -v pm2 &> /dev/null; then
        echo "📦 Installing PM2..."
        npm install -g pm2
    fi
    
    # Stop existing process if running
    pm2 stop agartha-multiplayer 2>/dev/null || true
    pm2 delete agartha-multiplayer 2>/dev/null || true
    
    # Start new process
    pm2 start server.js --name agartha-multiplayer
    pm2 save
    
    echo "✅ Server deployed with PM2"
    echo "📊 View logs: pm2 logs agartha-multiplayer"
    echo "🔄 Restart: pm2 restart agartha-multiplayer"
    echo "🛑 Stop: pm2 stop agartha-multiplayer"
}

# Function to prepare for cloud deployment
prepare_cloud() {
    echo "☁️ Preparing for cloud deployment..."
    
    # Create Procfile for Heroku
    if [ ! -f Procfile ]; then
        echo "web: node server.js" > Procfile
        echo "✅ Procfile created for Heroku"
    fi
    
    # Create app.json for Heroku
    if [ ! -f app.json ]; then
        cat > app.json << EOL
{
  "name": "agartha-rpg-multiplayer",
  "description": "Multiplayer server for Agartha RPG",
  "repository": "https://github.com/your-repo/agartha-rpg",
  "logo": "https://your-domain.com/logo.png",
  "keywords": ["rpg", "multiplayer", "websocket", "ai", "game"],
  "env": {
    "NODE_ENV": {
      "description": "Node environment",
      "value": "production"
    }
  },
  "formation": {
    "web": {
      "quantity": 1,
      "size": "hobby"
    }
  },
  "buildpacks": [
    {
      "url": "heroku/nodejs"
    }
  ]
}
EOL
        echo "✅ app.json created for Heroku"
    fi
    
    # Create railway.json for Railway
    if [ ! -f railway.json ]; then
        cat > railway.json << EOL
{
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "npm start",
    "healthcheckPath": "/",
    "healthcheckTimeout": 100,
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
EOL
        echo "✅ railway.json created for Railway"
    fi
    
    echo ""
    echo "📋 Cloud deployment files created. Next steps:"
    echo ""
    echo "🔸 Heroku:"
    echo "   heroku create your-app-name"
    echo "   git push heroku main"
    echo ""
    echo "🔸 Railway:"
    echo "   railway login"
    echo "   railway init"
    echo "   railway up"
    echo ""
    echo "🔸 DigitalOcean App Platform:"
    echo "   Connect your GitHub repo in the DO dashboard"
    echo ""
}

# Function to show server status
show_status() {
    echo "📊 Server Status"
    echo "==============="
    
    if command -v pm2 &> /dev/null; then
        pm2 list | grep agartha-multiplayer || echo "No PM2 processes found"
    fi
    
    # Check if port 3000 is in use
    if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "✅ Port 3000 is in use (server likely running)"
    else
        echo "❌ Port 3000 is not in use (server not running)"
    fi
}

# Function to run tests
run_tests() {
    echo "🧪 Running basic connectivity tests..."
    
    # Start server in background for testing
    npm start &
    SERVER_PID=$!
    
    # Wait for server to start
    sleep 3
    
    # Test HTTP endpoint
    if curl -f http://localhost:3000 >/dev/null 2>&1; then
        echo "✅ HTTP server is responding"
    else
        echo "❌ HTTP server is not responding"
    fi
    
    # Test WebSocket endpoint (basic check)
    if curl -f -H "Upgrade: websocket" http://localhost:3000/ws >/dev/null 2>&1; then
        echo "✅ WebSocket endpoint is accessible"
    else
        echo "⚠️ WebSocket endpoint check inconclusive (this is normal)"
    fi
    
    # Stop test server
    kill $SERVER_PID 2>/dev/null || true
    wait $SERVER_PID 2>/dev/null || true
    
    echo "✅ Basic tests completed"
}

# Main menu
echo ""
echo "Choose deployment option:"
echo "1) 🏠 Local development server"
echo "2) 🔄 Production server with PM2"
echo "3) ☁️ Prepare for cloud deployment"
echo "4) 📊 Show server status"
echo "5) 🧪 Run tests"
echo "6) ❌ Exit"
echo ""

read -p "Enter your choice (1-6): " choice

case $choice in
    1)
        deploy_local
        ;;
    2)
        deploy_pm2
        ;;
    3)
        prepare_cloud
        ;;
    4)
        show_status
        ;;
    5)
        run_tests
        ;;
    6)
        echo "👋 Goodbye!"
        exit 0
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "🎮 Deployment complete! Your Agartha multiplayer server is ready."
echo ""
echo "📖 For detailed setup instructions, see: MULTIPLAYER_SETUP.md"
echo "🌐 Access your game at: http://localhost:3000 (or your server URL)"
echo ""
