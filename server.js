/**
 * AGARTHA RPG - MULTIPLAYER SERVER
 * Simple WebSocket server for coordinating multiplayer games
 * One player hosts AI, others join and share game state
 */

const WebSocket = require('ws');
const http = require('http');
const path = require('path');
const fs = require('fs');

class AgarthaMultiplayerServer {
    constructor(port = 3000) {
        this.port = port;
        this.server = null;
        this.wss = null;
        
        // Game rooms storage
        this.gameRooms = new Map();
        this.playerConnections = new Map();
        
        console.log('🚀 Initializing Agartha Multiplayer Server...');
    }

    /**
     * Start the server
     */
    start() {
        // Create HTTP server for static files
        this.server = http.createServer((req, res) => {
            this.handleHttpRequest(req, res);
        });

        // Create WebSocket server
        this.wss = new WebSocket.Server({ 
            server: this.server,
            path: '/ws'
        });

        // Handle WebSocket connections
        this.wss.on('connection', (ws, req) => {
            this.handleWebSocketConnection(ws, req);
        });

        // Start listening
        this.server.listen(this.port, () => {
            console.log(`✅ Agartha Multiplayer Server running on port ${this.port}`);
            console.log(`🌐 WebSocket endpoint: ws://localhost:${this.port}/ws`);
            console.log(`📁 Static files served from current directory`);
        });

        // Handle server shutdown
        process.on('SIGINT', () => {
            console.log('\n🛑 Shutting down server...');
            this.shutdown();
        });
    }

    /**
     * Handle HTTP requests for static files
     */
    handleHttpRequest(req, res) {
        let filePath = req.url === '/' ? '/index.html' : req.url;
        filePath = path.join(__dirname, filePath);

        // Security check - prevent directory traversal
        if (!filePath.startsWith(__dirname)) {
            res.writeHead(403);
            res.end('Forbidden');
            return;
        }

        // Check if file exists
        fs.access(filePath, fs.constants.F_OK, (err) => {
            if (err) {
                res.writeHead(404);
                res.end('File not found');
                return;
            }

            // Get file extension for content type
            const ext = path.extname(filePath).toLowerCase();
            const contentTypes = {
                '.html': 'text/html',
                '.js': 'application/javascript',
                '.css': 'text/css',
                '.json': 'application/json',
                '.png': 'image/png',
                '.jpg': 'image/jpeg',
                '.gif': 'image/gif',
                '.svg': 'image/svg+xml'
            };

            const contentType = contentTypes[ext] || 'application/octet-stream';

            // Serve the file
            fs.readFile(filePath, (err, data) => {
                if (err) {
                    res.writeHead(500);
                    res.end('Server error');
                    return;
                }

                res.writeHead(200, { 'Content-Type': contentType });
                res.end(data);
            });
        });
    }

    /**
     * Handle new WebSocket connections
     */
    handleWebSocketConnection(ws, req) {
        const clientId = this.generateClientId();
        console.log(`🔌 New WebSocket connection: ${clientId}`);

        // Store connection
        this.playerConnections.set(clientId, {
            ws: ws,
            playerId: null,
            roomId: null,
            isHost: false,
            connectedAt: Date.now()
        });

        // Handle messages
        ws.on('message', (data) => {
            this.handleWebSocketMessage(clientId, data);
        });

        // Handle disconnection
        ws.on('close', () => {
            this.handleWebSocketDisconnection(clientId);
        });

        // Handle errors
        ws.on('error', (error) => {
            console.error(`WebSocket error for ${clientId}:`, error);
        });

        // Send welcome message
        this.sendToClient(clientId, {
            type: 'CONNECTED',
            clientId: clientId
        });
    }

    /**
     * Handle WebSocket messages
     */
    handleWebSocketMessage(clientId, data) {
        try {
            const message = JSON.parse(data);
            console.log(`📨 Message from ${clientId}: ${message.type}`);

            switch (message.type) {
                case 'HOST_GAME':
                    this.handleHostGame(clientId, message);
                    break;

                case 'JOIN_GAME':
                    this.handleJoinGame(clientId, message);
                    break;

                case 'REJOIN_HOST':
                case 'REJOIN_GAME':
                    this.handleRejoinGame(clientId, message);
                    break;

                case 'PLAYER_ACTION':
                    this.handlePlayerAction(clientId, message);
                    break;

                case 'GAME_STATE_UPDATE_BROADCAST':
                    this.handleGameStateUpdateBroadcast(clientId, message);
                    break;

                case 'AI_RESPONSE_BROADCAST':
                    this.handleAIResponseBroadcast(clientId, message);
                    break;

                default:
                    console.warn(`Unknown message type: ${message.type}`);
            }

        } catch (error) {
            console.error(`Error parsing message from ${clientId}:`, error);
            this.sendToClient(clientId, {
                type: 'ERROR',
                error: 'Invalid message format'
            });
        }
    }

    /**
     * Handle hosting a new game
     */
    handleHostGame(clientId, message) {
        const { roomId, playerId, playerData, aiModel, gameState } = message;

        // Check if room already exists
        if (this.gameRooms.has(roomId)) {
            this.sendToClient(clientId, {
                type: 'ERROR',
                error: 'Room already exists'
            });
            return;
        }

        // Create new game room
        const gameRoom = {
            id: roomId,
            hostClientId: clientId,
            hostPlayerId: playerId,
            aiModel: aiModel,
            gameState: gameState,
            players: new Map(),
            createdAt: Date.now()
        };

        // Add host as first player
        gameRoom.players.set(playerId, {
            clientId: clientId,
            playerData: playerData,
            isHost: true,
            joinedAt: Date.now()
        });

        this.gameRooms.set(roomId, gameRoom);

        // Update client connection info
        const connection = this.playerConnections.get(clientId);
        connection.playerId = playerId;
        connection.roomId = roomId;
        connection.isHost = true;

        console.log(`🎮 Game room created: ${roomId} by ${playerData.name}`);

        // Confirm to host
        this.sendToClient(clientId, {
            type: 'GAME_HOSTED',
            roomId: roomId,
            playerId: playerId
        });
    }

    /**
     * Handle joining an existing game
     */
    handleJoinGame(clientId, message) {
        const { roomId, playerId, playerData } = message;

        // Check if room exists
        const gameRoom = this.gameRooms.get(roomId);
        if (!gameRoom) {
            this.sendToClient(clientId, {
                type: 'ERROR',
                error: 'Game room not found'
            });
            return;
        }

        // Add player to room
        gameRoom.players.set(playerId, {
            clientId: clientId,
            playerData: playerData,
            isHost: false,
            joinedAt: Date.now()
        });

        // Update client connection info
        const connection = this.playerConnections.get(clientId);
        connection.playerId = playerId;
        connection.roomId = roomId;
        connection.isHost = false;

        console.log(`👤 Player ${playerData.name} joined room ${roomId}`);

        // Send game state to new player
        this.sendToClient(clientId, {
            type: 'GAME_JOINED',
            roomId: roomId,
            hostPlayerId: gameRoom.hostPlayerId,
            gameState: gameRoom.gameState,
            players: Array.from(gameRoom.players.values()).map(p => p.playerData)
        });

        // Notify other players
        this.broadcastToRoom(roomId, {
            type: 'PLAYER_JOINED',
            playerId: playerId,
            playerData: playerData
        }, clientId);
    }

    /**
     * Handle rejoining after disconnect
     */
    handleRejoinGame(clientId, message) {
        const { roomId, playerId } = message;

        const gameRoom = this.gameRooms.get(roomId);
        if (!gameRoom) {
            this.sendToClient(clientId, {
                type: 'ERROR',
                error: 'Game room not found'
            });
            return;
        }

        const player = gameRoom.players.get(playerId);
        if (!player) {
            this.sendToClient(clientId, {
                type: 'ERROR',
                error: 'Player not found in room'
            });
            return;
        }

        // Update client ID for reconnected player
        player.clientId = clientId;

        // Update connection info
        const connection = this.playerConnections.get(clientId);
        connection.playerId = playerId;
        connection.roomId = roomId;
        connection.isHost = player.isHost;

        console.log(`🔄 Player ${player.playerData.name} reconnected to room ${roomId}`);

        // Send current game state
        this.sendToClient(clientId, {
            type: 'GAME_JOINED',
            roomId: roomId,
            hostPlayerId: gameRoom.hostPlayerId,
            gameState: gameRoom.gameState,
            players: Array.from(gameRoom.players.values()).map(p => p.playerData)
        });
    }

    /**
     * Handle player actions
     */
    handlePlayerAction(clientId, message) {
        const { roomId, playerId, action, playerData } = message;

        const gameRoom = this.gameRooms.get(roomId);
        if (!gameRoom) return;

        // Forward action to host for AI processing
        const hostPlayer = Array.from(gameRoom.players.values()).find(p => p.isHost);
        if (hostPlayer && hostPlayer.clientId !== clientId) {
            this.sendToClient(hostPlayer.clientId, {
                type: 'PLAYER_ACTION',
                playerId: playerId,
                action: action,
                playerData: playerData
            });
        }
    }

    /**
     * Handle game state updates from host
     */
    handleGameStateUpdateBroadcast(clientId, message) {
        const { roomId, gameState, events } = message;

        const gameRoom = this.gameRooms.get(roomId);
        if (!gameRoom) return;

        // Update stored game state
        gameRoom.gameState = gameState;

        // Broadcast to all non-host players
        this.broadcastToRoom(roomId, {
            type: 'GAME_STATE_UPDATE',
            gameState: gameState,
            events: events
        }, clientId);
    }

    /**
     * Handle AI response broadcasts from host
     */
    handleAIResponseBroadcast(clientId, message) {
        const { roomId } = message;

        // Broadcast AI response to all non-host players
        this.broadcastToRoom(roomId, {
            type: 'AI_RESPONSE',
            ...message
        }, clientId);
    }

    /**
     * Handle WebSocket disconnection
     */
    handleWebSocketDisconnection(clientId) {
        console.log(`🔌 Client disconnected: ${clientId}`);

        const connection = this.playerConnections.get(clientId);
        if (connection && connection.roomId) {
            const gameRoom = this.gameRooms.get(connection.roomId);
            if (gameRoom) {
                // Notify other players
                this.broadcastToRoom(connection.roomId, {
                    type: 'PLAYER_LEFT',
                    playerId: connection.playerId
                }, clientId);

                // If host disconnected, we could transfer host or close room
                if (connection.isHost) {
                    console.log(`👑 Host disconnected from room ${connection.roomId}`);
                    // For now, keep the room open for potential reconnection
                }
            }
        }

        this.playerConnections.delete(clientId);
    }

    /**
     * Send message to specific client
     */
    sendToClient(clientId, message) {
        const connection = this.playerConnections.get(clientId);
        if (connection && connection.ws.readyState === WebSocket.OPEN) {
            connection.ws.send(JSON.stringify(message));
        }
    }

    /**
     * Broadcast message to all players in a room except sender
     */
    broadcastToRoom(roomId, message, excludeClientId = null) {
        const gameRoom = this.gameRooms.get(roomId);
        if (!gameRoom) return;

        gameRoom.players.forEach((player) => {
            if (player.clientId !== excludeClientId) {
                this.sendToClient(player.clientId, message);
            }
        });
    }

    /**
     * Generate unique client ID
     */
    generateClientId() {
        return 'client_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }

    /**
     * Clean up inactive rooms
     */
    cleanupInactiveRooms() {
        const now = Date.now();
        const maxInactiveTime = 24 * 60 * 60 * 1000; // 24 hours

        this.gameRooms.forEach((room, roomId) => {
            if (now - room.createdAt > maxInactiveTime) {
                console.log(`🧹 Cleaning up inactive room: ${roomId}`);
                this.gameRooms.delete(roomId);
            }
        });
    }

    /**
     * Shutdown server gracefully
     */
    shutdown() {
        if (this.wss) {
            this.wss.close();
        }
        if (this.server) {
            this.server.close();
        }
        console.log('✅ Server shutdown complete');
        process.exit(0);
    }
}

// Start server if this file is run directly
if (require.main === module) {
    const port = process.env.PORT || 3000;
    const server = new AgarthaMultiplayerServer(port);
    server.start();

    // Clean up inactive rooms every hour
    setInterval(() => {
        server.cleanupInactiveRooms();
    }, 60 * 60 * 1000);
}

module.exports = AgarthaMultiplayerServer;
