/**
 * AGARTHA RPG - GAME ENGINE
 * Core game logic and state management
 */

import { CONFIG } from './config.js';
import { LORE, getContextualLore } from './lore.js';

/**
 * Game Engine Class
 * Manages game state, mechanics, and core gameplay systems
 */
export class GameEngine {
    constructor(ai<PERSON><PERSON><PERSON>, character<PERSON>anager, saveManager) {
        this.aiManager = aiManager;
        this.characterManager = characterManager;
        this.saveManager = saveManager;
        
        // Game state
        this.gameState = {
            location: 'Crystal Gates of Shambhala',
            chapter: 1,
            timeOfDay: 'eternal twilight',
            players: new Map(),
            npcs: new Map(),
            inventory: new Map(),
            messageHistory: [],
            worldState: {
                discovered: ['Crystal Gates of Shambhala'],
                npcsmet: [],
                questsActive: [],
                questsCompleted: [],
                secrets: [],
                artifacts: []
            },
            gameStats: {
                actionsPerformed: 0,
                locationsVisited: 1,
                npcsEncountered: 0,
                questsCompleted: 0,
                startTime: Date.now(),
                playTime: 0
            }
        };
        
        // Game mechanics
        this.isProcessing = false;
        this.autoSaveTimer = null;
        this.lastActionTime = Date.now();
        
        // Event system
        this.eventListeners = new Map();
        
        this.initializeGameSystems();
    }

    /**
     * Initialize game systems
     */
    initializeGameSystems() {
        // Start auto-save timer
        if (CONFIG.game.autoSaveEnabled) {
            this.autoSaveTimer = setInterval(() => {
                this.autoSave();
            }, CONFIG.game.saveInterval);
        }
        
        // Initialize world state
        this.initializeWorld();
        
        console.log('🎮 Game Engine initialized');
    }

    /**
     * Initialize the game world
     */
    initializeWorld() {
        // Set up initial location
        const initialLocation = CONFIG.locations['Crystal Gates of Shambhala'];
        if (initialLocation) {
            this.gameState.location = initialLocation.name;
            this.gameState.worldState.discovered = [initialLocation.name];
        }
        
        // Initialize NPCs
        this.initializeNPCs();
        
        // Set up initial quests
        this.initializeQuests();
    }

    /**
     * Initialize NPCs in the world
     */
    initializeNPCs() {
        const initialNPCs = [
            {
                id: 'gate_guardian_zephyr',
                name: 'Gate Guardian Zephyr',
                location: 'Crystal Gates of Shambhala',
                type: 'guardian',
                personality: 'wise, ancient, protective',
                traits: {
                    wisdom: 95,
                    patience: 90,
                    authority: 85,
                    friendliness: 80,
                    protectiveness: 95
                },
                relationship: {
                    trust: 50,
                    respect: 60,
                    friendship: 40,
                    influence: 70
                },
                knowledge: ['ancient_history', 'portal_mechanics', 'visitor_guidance'],
                dialogue: {
                    greeting: "Welcome, surface dweller. I sense the awakening within you. The gates recognize your resonance.",
                    help: "The Crystal Gates respond to pure intention. Focus your mind and speak your purpose.",
                    lore: "These gates have stood for millennia, built by the first refugees from the surface catastrophes.",
                    moods: {
                        pleased: "Your progress brings joy to my ancient heart, young seeker.",
                        concerned: "I sense disturbance in your energy field. What troubles you?",
                        proud: "You have grown much since first passing through these gates.",
                        teaching: "Listen well, for this knowledge has been preserved for millennia.",
                        protective: "I will not allow harm to come to those under my protection."
                    }
                },
                questsOffered: ['gate_guardian_dialogue'],
                specialAbilities: ['energy_reading', 'gate_control', 'ancient_knowledge', 'protective_barrier'],
                backstory: "Zephyr has guarded the Crystal Gates for over 3,000 years, witnessing the arrival of countless seekers from the surface world. Once a powerful Atlantean mage, he chose to dedicate his extended life to protecting the entrance to Agartha.",
                currentMood: 'welcoming',
                interactionCount: 0,
                lastInteraction: null
            },
            {
                id: 'elder_thoth',
                name: 'Elder Thoth',
                location: 'Hall of Records',
                type: 'scholar',
                personality: 'wise, patient, knowledgeable',
                traits: {
                    wisdom: 100,
                    mystery: 95,
                    knowledge: 98,
                    patience: 85,
                    intuition: 92
                },
                relationship: {
                    trust: 40,
                    respect: 80,
                    friendship: 30,
                    influence: 95
                },
                knowledge: ['ancient_civilizations', 'akashic_records', 'cosmic_history'],
                dialogue: {
                    greeting: "Greetings, seeker. I am Thoth, keeper of the eternal records. What knowledge do you seek?",
                    help: "The Hall of Records contains the complete history of Earth and beyond. Ask, and you shall receive wisdom.",
                    lore: "I have witnessed the rise and fall of civilizations, the great migrations, and the cosmic cycles.",
                    moods: {
                        cryptic: "The answers you seek lie within questions you have not yet learned to ask.",
                        revealing: "The time has come to share what has long been hidden from surface dwellers.",
                        testing: "Prove your understanding before seeking deeper truths, young one.",
                        approving: "Your consciousness expands. This pleases the ancient ones.",
                        mysterious: "Some knowledge comes only to those who are truly ready to receive it."
                    }
                },
                questsOffered: ['elder_thoth_wisdom', 'akashic_access'],
                specialAbilities: ['prophecy', 'akashic_access', 'consciousness_reading', 'time_sight'],
                backstory: "Thoth is one of the original Council of Elders, present during the founding of Agartha itself. His knowledge spans the entire history of Earth's hidden civilizations. Some say he is the same Thoth who taught the ancient Egyptians.",
                currentMood: 'contemplative',
                interactionCount: 0,
                lastInteraction: null,
                teachingLevel: 'basic'
            },
            {
                id: 'high_priestess_adama',
                name: 'High Priestess Adama',
                location: 'Telos Beneath Mt. Shasta',
                type: 'priestess',
                personality: 'serene, telepathic, compassionate',
                traits: {
                    compassion: 100,
                    telepathy: 95,
                    healing: 90,
                    serenity: 98,
                    empathy: 96
                },
                relationship: {
                    trust: 70,
                    respect: 75,
                    friendship: 80,
                    influence: 85
                },
                knowledge: ['lemurian_wisdom', 'telepathy', 'consciousness_evolution'],
                dialogue: {
                    greeting: "Welcome, child of the surface. I am Adama, and I have been expecting you. Your thoughts reach me clearly.",
                    help: "The path of consciousness requires patience and practice. Let me guide you in the ancient ways.",
                    lore: "We Telosians are the descendants of Lemuria, keepers of the telepathic arts and guardians of inner wisdom.",
                    moods: {
                        nurturing: "Your spirit calls out for healing. Let me help you find peace within yourself.",
                        teaching: "The ways of Lemuria emphasize harmony between mind, body, and soul.",
                        concerned: "I sense pain in your energy field. We must address this before you can progress.",
                        proud: "Your telepathic abilities grow stronger. The ancestors smile upon your progress.",
                        meditative: "In stillness, we find the greatest truths. Let us meditate together."
                    }
                },
                questsOffered: ['telepathy_training', 'healing_chamber_experience', 'lemurian_history_quest'],
                specialAbilities: ['telepathy', 'healing', 'consciousness_expansion', 'energy_cleansing'],
                backstory: "Adama is a direct descendant of the original Lemurian refugees who founded Telos. She has dedicated her life to preserving Lemurian wisdom and helping others develop their psychic abilities. Her telepathic range extends across continents.",
                currentMood: 'serene',
                interactionCount: 0,
                lastInteraction: null,
                telepathicConnection: false
            },
            {
                id: 'vril_engineer_kael',
                name: 'Vril Engineer Kael',
                location: 'Vril Power Station',
                type: 'engineer',
                personality: 'technical, precise, innovative',
                traits: {
                    intelligence: 90,
                    enthusiasm: 95,
                    innovation: 88,
                    technical_skill: 92,
                    curiosity: 94
                },
                relationship: {
                    trust: 60,
                    respect: 65,
                    friendship: 70,
                    influence: 60
                },
                knowledge: ['vril_technology', 'energy_systems', 'atlantean_science'],
                dialogue: {
                    greeting: "Ah, a newcomer to the energy arts! I am Kael, master of vril manipulation. Ready to learn?",
                    help: "Vril is the fundamental force of life itself. With proper training, you can channel it safely.",
                    lore: "The Atlanteans first discovered vril, but we have perfected its use here in Agartha.",
                    moods: {
                        excited: "The possibilities are endless! Vril energy can transform everything we know!",
                        technical: "Let me explain the quantum resonance frequencies of the crystal matrices...",
                        concerned: "We must be careful. Vril energy demands respect and understanding.",
                        proud: "Your grasp of vril principles is impressive for a surface dweller!",
                        innovative: "I have an idea for a new application of vril technology!"
                    }
                },
                questsOffered: ['vril_theory_study', 'power_station_tour', 'energy_leak_repair'],
                specialAbilities: ['vril_manipulation', 'technology_interface', 'energy_analysis', 'invention'],
                backstory: "Kael is a brilliant Atlantean descendant who has dedicated his life to understanding and safely harnessing vril energy. He leads the team responsible for maintaining Agartha's power infrastructure and is always working on new innovations.",
                currentMood: 'enthusiastic',
                interactionCount: 0,
                lastInteraction: null,
                currentProject: 'energy_efficiency_optimization'
            },

            // Additional NPCs for richer world
            {
                id: 'librarian_sophia',
                name: 'Librarian Sophia',
                location: 'Hall of Records',
                type: 'scholar',
                personality: 'studious, helpful, detail-oriented',
                traits: {
                    knowledge: 85,
                    helpfulness: 95,
                    organization: 98,
                    patience: 80,
                    memory: 92
                },
                relationship: {
                    trust: 75,
                    respect: 70,
                    friendship: 85,
                    influence: 50
                },
                knowledge: ['library_systems', 'historical_records', 'research_methods'],
                dialogue: {
                    greeting: "Welcome to the Hall of Records! I'm Sophia, the head librarian. How may I assist your research?",
                    help: "Every piece of knowledge in existence is catalogued here. Let me help you find what you seek.",
                    lore: "The Hall contains not just books, but memory crystals, thought recordings, and living histories.",
                    moods: {
                        helpful: "I know exactly where to find that information! Follow me.",
                        excited: "Oh, you're interested in that topic? I have some fascinating sources!",
                        protective: "Please handle the ancient texts with care - they're irreplaceable.",
                        scholarly: "The cross-references in this section will provide deeper context."
                    }
                },
                questsOffered: ['hall_of_records_tour', 'research_assistance'],
                specialAbilities: ['information_retrieval', 'knowledge_synthesis', 'memory_crystal_reading'],
                backstory: "Sophia has been the head librarian for over 200 years, organizing and protecting the vast knowledge of Agartha. She knows the location of every piece of information in the Hall.",
                currentMood: 'welcoming',
                interactionCount: 0,
                lastInteraction: null
            },

            {
                id: 'merchant_kira',
                name: 'Merchant Kira',
                location: 'Market of Wonders',
                type: 'trader',
                personality: 'shrewd, friendly, worldly',
                traits: {
                    charisma: 88,
                    business_sense: 95,
                    worldliness: 90,
                    negotiation: 92,
                    trustworthiness: 85
                },
                relationship: {
                    trust: 65,
                    respect: 70,
                    friendship: 75,
                    influence: 60
                },
                knowledge: ['trade_routes', 'artifact_values', 'market_trends'],
                dialogue: {
                    greeting: "Welcome to my stall, traveler! I have wonders from across all the realms. What catches your eye?",
                    help: "Looking for something specific? I have connections throughout Agartha and beyond.",
                    lore: "The Market of Wonders brings together traders from every corner of the inner Earth.",
                    moods: {
                        business: "I can offer you a special price, but only because I like you!",
                        gossipy: "Have you heard the latest news from the outer tunnels?",
                        impressed: "You have excellent taste! That's a rare piece indeed.",
                        secretive: "I have some... special items in the back, if you're interested."
                    }
                },
                questsOffered: ['market_exploration', 'rare_artifact_hunt'],
                specialAbilities: ['appraisal', 'negotiation', 'network_access'],
                backstory: "Kira travels the vast network of Agartha, trading in rare artifacts and exotic goods. She has contacts in every major settlement and knows all the best trade routes.",
                currentMood: 'business',
                interactionCount: 0,
                lastInteraction: null,
                inventory: ['crystal_currency', 'rare_artifacts', 'trade_goods']
            }
        ];

        initialNPCs.forEach(npc => {
            this.gameState.npcs.set(npc.id, npc);
        });
    }

    /**
     * Initialize starting quests
     */
    initializeQuests() {
        const questChain = [
            // Chapter 1: Arrival
            {
                id: 'first_steps',
                title: 'First Steps in Agartha',
                description: 'Learn the basics of navigating the inner realm',
                chapter: 1,
                objectives: [
                    'Speak with Gate Guardian Zephyr',
                    'Examine the Crystal Gates',
                    'Enter Shambhala Central Plaza'
                ],
                subQuests: [
                    {
                        id: 'gate_guardian_dialogue',
                        title: 'The Guardian\'s Welcome',
                        description: 'Learn about Agartha\'s history from Zephyr',
                        objectives: ['Ask about the three civilizations', 'Learn about vril energy', 'Understand the Council of Elders'],
                        rewards: { experience: 50, items: ['Guardian\'s Blessing'] }
                    },
                    {
                        id: 'crystal_examination',
                        title: 'Resonance Testing',
                        description: 'Attune yourself to the Crystal Gates',
                        objectives: ['Touch the amethyst pillars', 'Feel the energy resonance', 'Activate your first crystal'],
                        rewards: { experience: 75, items: ['Resonance Crystal'] }
                    }
                ],
                rewards: {
                    experience: 100,
                    items: ['Newcomer\'s Crystal']
                },
                status: 'active',
                nextQuest: 'city_exploration'
            },

            // Chapter 2: City Exploration
            {
                id: 'city_exploration',
                title: 'Exploring the Great City',
                description: 'Discover the wonders of Shambhala Central Plaza',
                chapter: 2,
                objectives: [
                    'Visit the Hall of Records',
                    'Meet Elder Thoth',
                    'Learn about the three civilizations',
                    'Acquire a Communication Crystal'
                ],
                subQuests: [
                    {
                        id: 'hall_of_records_tour',
                        title: 'The Great Library',
                        description: 'Explore the vast repository of knowledge',
                        objectives: ['Find the Lemurian section', 'Read about Atlantean technology', 'Discover Agarthan history', 'Meet Librarian Sophia'],
                        rewards: { experience: 100, items: ['Knowledge Codex', 'Ancient Scroll'] }
                    },
                    {
                        id: 'market_exploration',
                        title: 'Market of Wonders',
                        description: 'Discover the trading hub of Agartha',
                        objectives: ['Meet Merchant Kira', 'Learn about crystal currency', 'Trade for supplies', 'Find rare artifacts'],
                        rewards: { experience: 75, items: ['Crystal Currency', 'Trader\'s Map'] }
                    },
                    {
                        id: 'elder_thoth_wisdom',
                        title: 'Wisdom of the Ages',
                        description: 'Receive teachings from the ancient Elder',
                        objectives: ['Learn meditation techniques', 'Understand cosmic cycles', 'Receive prophecy guidance', 'Unlock memory chambers'],
                        rewards: { experience: 150, items: ['Meditation Stone', 'Prophecy Scroll'] }
                    }
                ],
                sideQuests: [
                    {
                        id: 'lost_citizen',
                        title: 'The Lost Citizen',
                        description: 'Help a confused newcomer find their way',
                        objectives: ['Find the lost person', 'Guide them to safety', 'Learn their story'],
                        rewards: { experience: 50, items: ['Gratitude Token'] },
                        optional: true
                    },
                    {
                        id: 'crystal_repair',
                        title: 'Crystal Maintenance',
                        description: 'Assist with repairing damaged city crystals',
                        objectives: ['Diagnose crystal problems', 'Gather repair materials', 'Restore crystal function'],
                        rewards: { experience: 75, items: ['Repair Kit', 'Crystal Fragments'] },
                        optional: true
                    }
                ],
                rewards: {
                    experience: 250,
                    items: ['Communication Crystal', 'Ancient Map']
                },
                status: 'locked',
                nextQuest: 'vril_awakening'
            },

            // Chapter 3: Vril Awakening
            {
                id: 'vril_awakening',
                title: 'Awakening the Vril Within',
                description: 'Learn to harness the fundamental life force',
                chapter: 3,
                objectives: [
                    'Visit the Vril Power Station',
                    'Complete the Vril Attunement Ritual',
                    'Channel vril energy successfully',
                    'Speak with a Vril Engineer'
                ],
                subQuests: [
                    {
                        id: 'vril_theory_study',
                        title: 'Understanding Vril Energy',
                        description: 'Learn the theoretical foundations of vril manipulation',
                        objectives: ['Study energy frequency charts', 'Practice basic resonance', 'Understand safety protocols', 'Pass the knowledge test'],
                        rewards: { experience: 100, items: ['Vril Theory Manual', 'Frequency Tuner'] }
                    },
                    {
                        id: 'power_station_tour',
                        title: 'The Heart of Power',
                        description: 'Explore the massive energy distribution center',
                        objectives: ['Meet Engineer Kael', 'Tour the crystal matrices', 'Observe energy flows', 'Learn emergency procedures'],
                        rewards: { experience: 125, items: ['Station Access Card', 'Energy Scanner'] }
                    },
                    {
                        id: 'attunement_preparation',
                        title: 'Ritual Preparation',
                        description: 'Prepare mind and body for vril attunement',
                        objectives: ['Purify through meditation', 'Fast for spiritual clarity', 'Gather ritual components', 'Find a spiritual guide'],
                        rewards: { experience: 75, items: ['Purification Crystals', 'Ritual Robes'] }
                    },
                    {
                        id: 'first_channeling',
                        title: 'First Contact with Vril',
                        description: 'Attempt your first conscious vril manipulation',
                        objectives: ['Enter meditative state', 'Feel the energy flow', 'Direct vril consciously', 'Maintain control'],
                        rewards: { experience: 150, items: ['Personal Vril Focus', 'Energy Stabilizer'] }
                    }
                ],
                sideQuests: [
                    {
                        id: 'energy_leak_repair',
                        title: 'Emergency Repair',
                        description: 'Help fix a dangerous energy leak in the station',
                        objectives: ['Locate the leak source', 'Contain the energy', 'Repair the conduit', 'Test the fix'],
                        rewards: { experience: 100, items: ['Emergency Kit', 'Engineer\'s Commendation'] },
                        optional: true
                    },
                    {
                        id: 'vril_sensitivity_test',
                        title: 'Sensitivity Assessment',
                        description: 'Undergo testing to measure your vril sensitivity',
                        objectives: ['Complete energy detection tests', 'Demonstrate resonance ability', 'Show energy manipulation', 'Receive rating'],
                        rewards: { experience: 75, items: ['Sensitivity Certificate', 'Aptitude Badge'] },
                        optional: true
                    }
                ],
                rewards: {
                    experience: 400,
                    items: ['Vril Conductor', 'Energy Amplifier']
                },
                status: 'locked',
                nextQuest: 'lemurian_wisdom'
            },

            // Chapter 4: Lemurian Wisdom
            {
                id: 'lemurian_wisdom',
                title: 'Seeking Lemurian Wisdom',
                description: 'Journey to Telos and learn from the Telosians',
                chapter: 4,
                objectives: [
                    'Travel to Telos beneath Mt. Shasta',
                    'Meet High Priestess Adama',
                    'Learn telepathic communication',
                    'Access the Akashic Records'
                ],
                subQuests: [
                    {
                        id: 'journey_to_telos',
                        title: 'The Sacred Journey',
                        description: 'Navigate the tunnel systems to reach Telos',
                        objectives: ['Find the hidden entrance', 'Navigate crystal caverns', 'Pass the guardian tests', 'Reach the city gates'],
                        rewards: { experience: 150, items: ['Pathfinder\'s Badge', 'Crystal Compass'] }
                    },
                    {
                        id: 'telosian_customs',
                        title: 'Learning Telosian Ways',
                        description: 'Understand the culture and customs of Telos',
                        objectives: ['Learn greeting rituals', 'Understand social hierarchy', 'Practice energy etiquette', 'Receive cultural blessing'],
                        rewards: { experience: 100, items: ['Cultural Guide', 'Telosian Robes'] }
                    },
                    {
                        id: 'telepathy_training',
                        title: 'Mind-to-Mind Communication',
                        description: 'Develop your telepathic abilities',
                        objectives: ['Clear mental static', 'Practice thought projection', 'Receive telepathic messages', 'Establish mind-link'],
                        rewards: { experience: 200, items: ['Telepathic Focus', 'Mind Shield'] }
                    },
                    {
                        id: 'akashic_access',
                        title: 'The Universal Records',
                        description: 'Gain access to the cosmic memory bank',
                        objectives: ['Prepare consciousness', 'Enter the Akashic realm', 'Navigate information streams', 'Download key knowledge'],
                        rewards: { experience: 250, items: ['Akashic Key', 'Memory Crystal'] }
                    }
                ],
                sideQuests: [
                    {
                        id: 'healing_chamber_experience',
                        title: 'The Healing Chambers',
                        description: 'Experience Lemurian healing technology',
                        objectives: ['Enter healing chamber', 'Undergo energy cleansing', 'Receive cellular repair', 'Learn healing techniques'],
                        rewards: { experience: 125, items: ['Healing Crystal', 'Rejuvenation Elixir'] },
                        optional: true
                    },
                    {
                        id: 'crystal_garden_meditation',
                        title: 'Garden of Living Crystals',
                        description: 'Meditate in the sacred crystal gardens',
                        objectives: ['Find inner peace', 'Commune with crystal beings', 'Receive nature wisdom', 'Plant a memory seed'],
                        rewards: { experience: 100, items: ['Garden Blessing', 'Living Crystal Seed'] },
                        optional: true
                    },
                    {
                        id: 'lemurian_history_quest',
                        title: 'Echoes of Mu',
                        description: 'Learn the true history of the Lemurian civilization',
                        objectives: ['Visit memory halls', 'Witness historical visions', 'Understand the great sinking', 'Honor the ancestors'],
                        rewards: { experience: 150, items: ['Historical Codex', 'Ancestor Blessing'] },
                        optional: true
                    }
                ],
                rewards: {
                    experience: 600,
                    items: ['Telepathic Amplifier', 'Memory Stone']
                },
                status: 'locked',
                nextQuest: 'atlantean_legacy'
            },

            // Chapter 5: Atlantean Legacy
            {
                id: 'atlantean_legacy',
                title: 'Uncovering Atlantean Secrets',
                description: 'Discover the advanced technology of lost Atlantis',
                chapter: 5,
                objectives: [
                    'Find the hidden Atlantean laboratory',
                    'Activate ancient crystal technology',
                    'Learn about the Great Cataclysm',
                    'Retrieve Atlantean artifacts'
                ],
                subQuests: [
                    {
                        id: 'laboratory_discovery',
                        title: 'The Hidden Laboratory',
                        description: 'Locate the secret Atlantean research facility',
                        objectives: ['Decode ancient maps', 'Navigate underwater passages', 'Bypass security systems', 'Enter the laboratory'],
                        rewards: { experience: 175, items: ['Laboratory Access Key', 'Ancient Map Fragment'] }
                    },
                    {
                        id: 'technology_activation',
                        title: 'Awakening Ancient Machines',
                        description: 'Restore power to Atlantean technology',
                        objectives: ['Understand crystal matrices', 'Repair power conduits', 'Calibrate energy flows', 'Activate main systems'],
                        rewards: { experience: 200, items: ['Power Core', 'System Interface'] }
                    },
                    {
                        id: 'cataclysm_investigation',
                        title: 'The Great Destruction',
                        description: 'Uncover the truth behind Atlantis\'s fall',
                        objectives: ['Access historical databases', 'Review experiment logs', 'Understand vril misuse', 'Learn from mistakes'],
                        rewards: { experience: 225, items: ['Historical Database', 'Warning Protocols'] }
                    },
                    {
                        id: 'genetic_research',
                        title: 'The Enhancement Project',
                        description: 'Discover Atlantean genetic manipulation research',
                        objectives: ['Access genetic labs', 'Study enhancement formulas', 'Understand DNA modifications', 'Evaluate ethical implications'],
                        rewards: { experience: 200, items: ['Genetic Codex', 'Enhancement Serum'] }
                    }
                ],
                sideQuests: [
                    {
                        id: 'atlantean_warrior_training',
                        title: 'Warrior\'s Legacy',
                        description: 'Train in ancient Atlantean combat techniques',
                        objectives: ['Learn crystal weapon mastery', 'Practice energy combat', 'Master defensive techniques', 'Earn warrior rank'],
                        rewards: { experience: 150, items: ['Crystal Weapon', 'Warrior\'s Insignia'] },
                        optional: true
                    },
                    {
                        id: 'crystal_technology_mastery',
                        title: 'Crystal Tech Specialist',
                        description: 'Master the use of Atlantean crystal technology',
                        objectives: ['Study crystal programming', 'Create energy constructs', 'Build crystal devices', 'Demonstrate mastery'],
                        rewards: { experience: 175, items: ['Crystal Toolkit', 'Tech Mastery Certificate'] },
                        optional: true
                    },
                    {
                        id: 'atlantean_survivor_stories',
                        title: 'Voices from the Past',
                        description: 'Collect testimonies from Atlantean survivors',
                        objectives: ['Find elderly survivors', 'Record their stories', 'Preserve their memories', 'Honor their sacrifice'],
                        rewards: { experience: 125, items: ['Memory Archive', 'Survivor\'s Blessing'] },
                        optional: true
                    }
                ],
                rewards: {
                    experience: 800,
                    items: ['Atlantean Crystal Core', 'Genetic Scanner']
                },
                status: 'locked',
                nextQuest: 'inner_sun_pilgrimage'
            },

            // Chapter 6: The Inner Sun
            {
                id: 'inner_sun_pilgrimage',
                title: 'Pilgrimage to the Central Sun',
                description: 'Journey to the heart of Agartha',
                chapter: 6,
                objectives: [
                    'Prepare for the journey to the Central Sun',
                    'Navigate the Deep Tunnels',
                    'Overcome the Guardian Trials',
                    'Reach the Chamber of the Smoky God'
                ],
                subQuests: [
                    {
                        id: 'pilgrimage_preparation',
                        title: 'Sacred Preparation',
                        description: 'Prepare spiritually and physically for the sacred journey',
                        objectives: ['Undergo purification rituals', 'Gather sacred supplies', 'Receive elder blessings', 'Meditate on purpose'],
                        rewards: { experience: 200, items: ['Pilgrim\'s Staff', 'Sacred Provisions'] }
                    },
                    {
                        id: 'deep_tunnel_navigation',
                        title: 'Into the Depths',
                        description: 'Navigate the treacherous deep tunnel systems',
                        objectives: ['Map tunnel routes', 'Avoid dangerous creatures', 'Find safe rest points', 'Maintain group cohesion'],
                        rewards: { experience: 250, items: ['Deep Map', 'Survival Kit'] }
                    },
                    {
                        id: 'guardian_trials',
                        title: 'Trials of Worthiness',
                        description: 'Face the tests set by the ancient guardians',
                        objectives: ['Trial of Courage', 'Trial of Wisdom', 'Trial of Compassion', 'Trial of Unity'],
                        rewards: { experience: 400, items: ['Guardian Token', 'Trial Certificate'] }
                    },
                    {
                        id: 'central_sun_approach',
                        title: 'Approaching the Divine',
                        description: 'Make the final approach to the Central Sun',
                        objectives: ['Shield from intense energy', 'Maintain consciousness', 'Approach with reverence', 'Enter the chamber'],
                        rewards: { experience: 350, items: ['Solar Shield', 'Divine Blessing'] }
                    }
                ],
                sideQuests: [
                    {
                        id: 'lost_pilgrims_rescue',
                        title: 'Rescue the Lost',
                        description: 'Find and rescue pilgrims lost in the deep tunnels',
                        objectives: ['Track missing pilgrims', 'Navigate dangerous areas', 'Provide aid and guidance', 'Lead them to safety'],
                        rewards: { experience: 200, items: ['Rescue Medal', 'Gratitude Tokens'] },
                        optional: true
                    },
                    {
                        id: 'ancient_shrine_discovery',
                        title: 'Forgotten Shrines',
                        description: 'Discover and restore ancient shrines along the path',
                        objectives: ['Find hidden shrines', 'Cleanse corrupted energy', 'Restore sacred symbols', 'Reactivate blessings'],
                        rewards: { experience: 175, items: ['Shrine Key', 'Ancient Blessing'] },
                        optional: true
                    },
                    {
                        id: 'guardian_communion',
                        title: 'Speaking with Guardians',
                        description: 'Communicate with the ancient guardian spirits',
                        objectives: ['Learn guardian language', 'Show proper respect', 'Receive ancient wisdom', 'Gain their favor'],
                        rewards: { experience: 225, items: ['Guardian Wisdom', 'Spirit Guide'] },
                        optional: true
                    }
                ],
                rewards: {
                    experience: 1200,
                    items: ['Solar Crystal', 'Guardian\'s Blessing']
                },
                status: 'locked',
                nextQuest: 'cosmic_revelation'
            },

            // Chapter 7: Cosmic Truth
            {
                id: 'cosmic_revelation',
                title: 'The Cosmic Revelation',
                description: 'Uncover the ultimate truth about Earth and humanity',
                chapter: 7,
                objectives: [
                    'Commune with the Central Sun consciousness',
                    'Learn about Earth\'s true history',
                    'Understand humanity\'s cosmic purpose',
                    'Receive the Star Seed activation'
                ],
                subQuests: [
                    {
                        id: 'consciousness_communion',
                        title: 'Divine Communication',
                        description: 'Establish direct communication with the Central Sun consciousness',
                        objectives: ['Raise vibrational frequency', 'Open consciousness channels', 'Receive divine transmission', 'Integrate cosmic knowledge'],
                        rewards: { experience: 300, items: ['Consciousness Amplifier', 'Divine Frequency'] }
                    },
                    {
                        id: 'earth_history_revelation',
                        title: 'The True History',
                        description: 'Learn the complete history of Earth and its civilizations',
                        objectives: ['Access cosmic records', 'Understand galactic connections', 'Learn about star seeds', 'Discover Earth\'s purpose'],
                        rewards: { experience: 350, items: ['Cosmic Chronicle', 'Galactic Map'] }
                    },
                    {
                        id: 'humanity_purpose_understanding',
                        title: 'Humanity\'s Destiny',
                        description: 'Understand the true purpose of human consciousness',
                        objectives: ['Learn about consciousness evolution', 'Understand dimensional ascension', 'Discover human potential', 'Accept cosmic responsibility'],
                        rewards: { experience: 400, items: ['Purpose Crystal', 'Destiny Scroll'] }
                    },
                    {
                        id: 'star_seed_activation',
                        title: 'Awakening the Star Seed',
                        description: 'Activate your dormant cosmic DNA',
                        objectives: ['Prepare for activation', 'Undergo energy transformation', 'Integrate new abilities', 'Test enhanced consciousness'],
                        rewards: { experience: 450, items: ['Activated Star Seed', 'Cosmic Abilities'] }
                    }
                ],
                sideQuests: [
                    {
                        id: 'cosmic_library_access',
                        title: 'The Universal Library',
                        description: 'Access the cosmic library of all knowledge',
                        objectives: ['Prove worthiness', 'Navigate information streams', 'Download key knowledge', 'Share with others'],
                        rewards: { experience: 250, items: ['Universal Access Key', 'Knowledge Downloads'] },
                        optional: true
                    },
                    {
                        id: 'dimensional_travel_training',
                        title: 'Between Dimensions',
                        description: 'Learn to travel between dimensional planes',
                        objectives: ['Understand dimensional theory', 'Practice phase shifting', 'Visit parallel realms', 'Master dimensional navigation'],
                        rewards: { experience: 300, items: ['Dimensional Key', 'Phase Shifter'] },
                        optional: true
                    }
                ],
                rewards: {
                    experience: 1500,
                    items: ['Star Seed', 'Cosmic Key']
                },
                status: 'locked',
                nextQuest: 'surface_mission'
            },

            // Chapter 8: The Return
            {
                id: 'surface_mission',
                title: 'Mission to the Surface',
                description: 'Return to the surface world as an awakened being',
                chapter: 8,
                objectives: [
                    'Prepare for the return journey',
                    'Establish communication networks',
                    'Begin the Great Awakening',
                    'Unite the surface and inner worlds'
                ],
                subQuests: [
                    {
                        id: 'return_preparation',
                        title: 'Preparing for Return',
                        description: 'Make final preparations for the journey back to the surface',
                        objectives: ['Gather mission supplies', 'Receive final teachings', 'Create communication devices', 'Plan awakening strategy'],
                        rewards: { experience: 400, items: ['Mission Kit', 'Communication Array'] }
                    },
                    {
                        id: 'network_establishment',
                        title: 'Building the Network',
                        description: 'Establish communication links between surface and inner Earth',
                        objectives: ['Install relay stations', 'Test communication links', 'Train surface contacts', 'Secure transmission channels'],
                        rewards: { experience: 500, items: ['Network Controller', 'Relay Crystals'] }
                    },
                    {
                        id: 'awakening_initiation',
                        title: 'The Great Awakening Begins',
                        description: 'Start the process of awakening surface humanity',
                        objectives: ['Identify awakening candidates', 'Share hidden knowledge', 'Demonstrate new abilities', 'Form awakening groups'],
                        rewards: { experience: 600, items: ['Awakening Catalyst', 'Truth Crystals'] }
                    },
                    {
                        id: 'world_unification',
                        title: 'Bridging Two Worlds',
                        description: 'Work to unite the surface and inner Earth civilizations',
                        objectives: ['Arrange first contact meetings', 'Facilitate cultural exchange', 'Address fears and concerns', 'Establish cooperation protocols'],
                        rewards: { experience: 500, items: ['Unity Medallion', 'Diplomatic Seal'] }
                    }
                ],
                sideQuests: [
                    {
                        id: 'resistance_management',
                        title: 'Overcoming Resistance',
                        description: 'Deal with those who oppose the awakening',
                        objectives: ['Identify opposition sources', 'Address their concerns', 'Demonstrate peaceful intent', 'Convert skeptics'],
                        rewards: { experience: 300, items: ['Diplomacy Badge', 'Peace Offering'] },
                        optional: true
                    },
                    {
                        id: 'technology_sharing',
                        title: 'Sharing Advanced Technology',
                        description: 'Carefully introduce Agarthan technology to the surface',
                        objectives: ['Select appropriate technologies', 'Train surface scientists', 'Ensure safe implementation', 'Monitor progress'],
                        rewards: { experience: 350, items: ['Tech Transfer License', 'Safety Protocols'] },
                        optional: true
                    },
                    {
                        id: 'environmental_healing',
                        title: 'Healing the Earth',
                        description: 'Use Agarthan knowledge to heal environmental damage',
                        objectives: ['Assess environmental damage', 'Apply healing technologies', 'Restore natural balance', 'Train surface healers'],
                        rewards: { experience: 400, items: ['Earth Healing Kit', 'Restoration Manual'] },
                        optional: true
                    }
                ],
                rewards: {
                    experience: 2000,
                    items: ['Ambassador\'s Seal', 'Unity Crystal']
                },
                status: 'locked',
                nextQuest: 'golden_age'
            },

            // Final Chapter: Golden Age
            {
                id: 'golden_age',
                title: 'Herald of the Golden Age',
                description: 'Usher in the new era of human consciousness',
                chapter: 9,
                objectives: [
                    'Establish the New Earth Council',
                    'Activate the global crystal network',
                    'Awaken humanity\'s dormant abilities',
                    'Complete the Great Work'
                ],
                subQuests: [
                    {
                        id: 'council_establishment',
                        title: 'The New Earth Council',
                        description: 'Form a governing body to guide the new age',
                        objectives: ['Select council members', 'Establish council chambers', 'Create governing principles', 'Hold first council session'],
                        rewards: { experience: 600, items: ['Council Seal', 'Leadership Staff'] }
                    },
                    {
                        id: 'crystal_network_activation',
                        title: 'Global Crystal Grid',
                        description: 'Activate the worldwide crystal energy network',
                        objectives: ['Locate all grid points', 'Synchronize crystal frequencies', 'Channel activation energy', 'Monitor global effects'],
                        rewards: { experience: 800, items: ['Grid Master Key', 'Global Monitor'] }
                    },
                    {
                        id: 'mass_awakening',
                        title: 'Humanity\'s Awakening',
                        description: 'Trigger the mass awakening of human consciousness',
                        objectives: ['Prepare awakening frequencies', 'Coordinate global transmission', 'Guide newly awakened', 'Establish support networks'],
                        rewards: { experience: 1000, items: ['Awakening Beacon', 'Consciousness Guide'] }
                    },
                    {
                        id: 'great_work_completion',
                        title: 'The Great Work',
                        description: 'Complete the ancient alchemical Great Work',
                        objectives: ['Understand the Great Work', 'Gather final components', 'Perform the ultimate ritual', 'Achieve planetary transformation'],
                        rewards: { experience: 1200, items: ['Philosopher\'s Stone', 'Transformation Key'] }
                    }
                ],
                sideQuests: [
                    {
                        id: 'galactic_contact',
                        title: 'First Galactic Contact',
                        description: 'Establish contact with galactic civilizations',
                        objectives: ['Prepare for contact', 'Send galactic signals', 'Receive first response', 'Establish diplomatic relations'],
                        rewards: { experience: 500, items: ['Galactic Communicator', 'Star Ambassador Badge'] },
                        optional: true
                    },
                    {
                        id: 'dimensional_gateway',
                        title: 'Opening the Gateways',
                        description: 'Open permanent gateways between dimensions',
                        objectives: ['Locate gateway points', 'Stabilize dimensional rifts', 'Create permanent portals', 'Establish interdimensional travel'],
                        rewards: { experience: 600, items: ['Gateway Key', 'Dimensional Passport'] },
                        optional: true
                    },
                    {
                        id: 'planetary_healing',
                        title: 'Complete Planetary Restoration',
                        description: 'Fully restore Earth to its pristine state',
                        objectives: ['Heal all ecosystems', 'Restore extinct species', 'Purify all waters', 'Regenerate the atmosphere'],
                        rewards: { experience: 700, items: ['Gaia\'s Blessing', 'Restoration Crown'] },
                        optional: true
                    },
                    {
                        id: 'consciousness_ascension',
                        title: 'Collective Ascension',
                        description: 'Guide humanity through dimensional ascension',
                        objectives: ['Prepare ascension protocols', 'Raise planetary frequency', 'Guide mass ascension', 'Establish new dimensional home'],
                        rewards: { experience: 1000, items: ['Ascension Key', 'Dimensional Crown'] },
                        optional: true
                    }
                ],
                rewards: {
                    experience: 3000,
                    items: ['Crown of Light', 'Master\'s Staff']
                },
                status: 'locked',
                isEndGame: true
            }
        ];

        this.gameState.worldState.questsActive = [questChain[0]]; // Start with first quest
        this.gameState.worldState.questChain = questChain;
        this.gameState.worldState.questsCompleted = [];

        // Initialize advanced game systems
        this.initializeCraftingSystem();
        this.initializeTradingSystem();
        this.initializeCombatSystem();
        this.initializeResourceManagement();
    }

    /**
     * Initialize Crafting System
     */
    initializeCraftingSystem() {
        this.gameState.crafting = {
            recipes: new Map(),
            materials: new Map(),
            workshops: new Map(),
            craftingQueue: [],
            masteryCrafters: new Set()
        };

        // Define crafting recipes
        const recipes = [
            {
                id: 'basic_crystal_focus',
                name: 'Basic Crystal Focus',
                category: 'mystical',
                materials: { 'raw_crystal': 3, 'silver_wire': 2, 'meditation_oil': 1 },
                tools: ['crystal_forge'],
                skill_required: { crystalResonance: 10 },
                time: 30, // minutes
                result: { item: 'Crystal Focus', quantity: 1, quality: 'basic' },
                experience: { crystalResonance: 25, technology: 10 }
            },
            {
                id: 'vril_conductor',
                name: 'Vril Conductor',
                category: 'technology',
                materials: { 'atlantean_metal': 2, 'energy_crystal': 1, 'conductive_fiber': 3 },
                tools: ['energy_forge', 'precision_tools'],
                skill_required: { vrilManipulation: 15, technology: 12 },
                time: 45,
                result: { item: 'Vril Conductor', quantity: 1, quality: 'standard' },
                experience: { vrilManipulation: 35, technology: 25 }
            },
            {
                id: 'healing_elixir',
                name: 'Healing Elixir',
                category: 'alchemy',
                materials: { 'healing_herbs': 5, 'crystal_water': 2, 'life_essence': 1 },
                tools: ['alchemy_lab'],
                skill_required: { energyHealing: 8, ancientLore: 5 },
                time: 20,
                result: { item: 'Healing Elixir', quantity: 3, quality: 'potent' },
                experience: { energyHealing: 20, ancientLore: 10 }
            },
            {
                id: 'dimensional_key',
                name: 'Dimensional Key',
                category: 'mystical',
                materials: { 'void_crystal': 1, 'temporal_metal': 2, 'consciousness_essence': 1 },
                tools: ['dimensional_forge', 'reality_anchor'],
                skill_required: { dimensionalAwareness: 25, vrilManipulation: 20 },
                time: 120,
                result: { item: 'Dimensional Key', quantity: 1, quality: 'masterwork' },
                experience: { dimensionalAwareness: 50, vrilManipulation: 30 }
            }
        ];

        recipes.forEach(recipe => {
            this.gameState.crafting.recipes.set(recipe.id, recipe);
        });

        // Initialize material database
        const materials = [
            { id: 'raw_crystal', name: 'Raw Crystal', rarity: 'common', sources: ['mining', 'exploration'] },
            { id: 'silver_wire', name: 'Silver Wire', rarity: 'common', sources: ['trading', 'crafting'] },
            { id: 'meditation_oil', name: 'Meditation Oil', rarity: 'uncommon', sources: ['alchemy', 'trading'] },
            { id: 'atlantean_metal', name: 'Atlantean Metal', rarity: 'rare', sources: ['ruins', 'trading'] },
            { id: 'energy_crystal', name: 'Energy Crystal', rarity: 'uncommon', sources: ['power_stations', 'mining'] },
            { id: 'void_crystal', name: 'Void Crystal', rarity: 'legendary', sources: ['dimensional_rifts'] },
            { id: 'temporal_metal', name: 'Temporal Metal', rarity: 'epic', sources: ['time_anomalies'] },
            { id: 'consciousness_essence', name: 'Consciousness Essence', rarity: 'legendary', sources: ['meditation', 'transcendence'] }
        ];

        materials.forEach(material => {
            this.gameState.crafting.materials.set(material.id, material);
        });
    }

    /**
     * Initialize Trading System
     */
    initializeTradingSystem() {
        this.gameState.trading = {
            currency: new Map([
                ['crystal_shards', 100], // Starting currency
                ['energy_units', 0],
                ['knowledge_tokens', 0],
                ['reputation_points', 0]
            ]),
            marketPrices: new Map(),
            tradeRoutes: new Map(),
            merchants: new Map(),
            tradeHistory: []
        };

        // Initialize market prices (fluctuate based on supply/demand)
        const marketItems = [
            { id: 'healing_elixir', basePrice: 25, currentPrice: 25, demand: 'medium' },
            { id: 'crystal_focus', basePrice: 150, currentPrice: 150, demand: 'high' },
            { id: 'vril_conductor', basePrice: 300, currentPrice: 300, demand: 'medium' },
            { id: 'ancient_scroll', basePrice: 75, currentPrice: 75, demand: 'low' },
            { id: 'energy_crystal', basePrice: 50, currentPrice: 50, demand: 'high' },
            { id: 'atlantean_artifact', basePrice: 500, currentPrice: 500, demand: 'very_high' }
        ];

        marketItems.forEach(item => {
            this.gameState.trading.marketPrices.set(item.id, item);
        });

        // Initialize trade routes
        const tradeRoutes = [
            {
                id: 'shambhala_telos',
                from: 'Shambhala Central Plaza',
                to: 'Telos Beneath Mt. Shasta',
                distance: 500,
                travelTime: 120, // minutes
                dangers: ['energy_storms'],
                specialties: ['telepathic_amplifiers', 'consciousness_crystals']
            },
            {
                id: 'market_outer_reaches',
                from: 'Market of Wonders',
                to: 'Outer Reaches',
                distance: 800,
                travelTime: 200,
                dangers: ['wild_energy', 'unstable_terrain'],
                specialties: ['rare_minerals', 'ancient_relics']
            }
        ];

        tradeRoutes.forEach(route => {
            this.gameState.trading.tradeRoutes.set(route.id, route);
        });
    }

    /**
     * Initialize Combat System
     */
    initializeCombatSystem() {
        this.gameState.combat = {
            activeEncounters: new Map(),
            combatLog: [],
            weaponTypes: new Map(),
            armorTypes: new Map(),
            combatStyles: new Map(),
            statusEffects: new Map()
        };

        // Define weapon types
        const weaponTypes = [
            {
                id: 'crystal_blade',
                name: 'Crystal Blade',
                type: 'melee',
                damage: { min: 15, max: 25 },
                special: 'energy_channeling',
                requirements: { melee: 10, crystalResonance: 5 }
            },
            {
                id: 'vril_staff',
                name: 'Vril Staff',
                type: 'mystical',
                damage: { min: 20, max: 30 },
                special: 'vril_blast',
                requirements: { vrilManipulation: 15 }
            },
            {
                id: 'atlantean_bow',
                name: 'Atlantean Bow',
                type: 'ranged',
                damage: { min: 12, max: 20 },
                special: 'energy_arrows',
                requirements: { ranged: 12, technology: 8 }
            },
            {
                id: 'consciousness_weapon',
                name: 'Consciousness Weapon',
                type: 'mental',
                damage: { min: 25, max: 35 },
                special: 'mind_disruption',
                requirements: { telepathy: 20, dimensionalAwareness: 15 }
            }
        ];

        weaponTypes.forEach(weapon => {
            this.gameState.combat.weaponTypes.set(weapon.id, weapon);
        });

        // Define combat styles
        const combatStyles = [
            {
                id: 'atlantean_warrior',
                name: 'Atlantean Combat Style',
                bonuses: { melee: 2, defense: 2, tactics: 1 },
                techniques: ['power_strike', 'defensive_stance', 'battle_fury']
            },
            {
                id: 'lemurian_harmony',
                name: 'Lemurian Harmony Style',
                bonuses: { telepathy: 3, empathy: 2, energyHealing: 1 },
                techniques: ['mind_link', 'peaceful_resolution', 'energy_shield']
            },
            {
                id: 'vril_mastery',
                name: 'Vril Mastery Style',
                bonuses: { vrilManipulation: 3, technology: 2, dimensionalAwareness: 1 },
                techniques: ['energy_blast', 'vril_shield', 'dimensional_step']
            }
        ];

        combatStyles.forEach(style => {
            this.gameState.combat.combatStyles.set(style.id, style);
        });
    }

    /**
     * Initialize Resource Management
     */
    initializeResourceManagement() {
        this.gameState.resources = {
            energy: { current: 100, max: 100, regeneration: 1 }, // per minute
            focus: { current: 50, max: 50, regeneration: 0.5 },
            materials: new Map(),
            storage: { current: 0, max: 100, upgrades: [] },
            production: new Map(),
            consumption: new Map()
        };

        // Initialize material storage
        const startingMaterials = [
            { id: 'raw_crystal', quantity: 10 },
            { id: 'silver_wire', quantity: 5 },
            { id: 'healing_herbs', quantity: 8 },
            { id: 'crystal_water', quantity: 3 }
        ];

        startingMaterials.forEach(material => {
            this.gameState.resources.materials.set(material.id, material.quantity);
        });

        // Initialize production facilities
        const productionFacilities = [
            {
                id: 'crystal_garden',
                name: 'Crystal Garden',
                produces: 'raw_crystal',
                rate: 2, // per hour
                requirements: { location: 'any', skill: { crystalResonance: 5 } }
            },
            {
                id: 'meditation_grove',
                name: 'Meditation Grove',
                produces: 'meditation_oil',
                rate: 1,
                requirements: { location: 'peaceful', skill: { energyHealing: 8 } }
            },
            {
                id: 'energy_collector',
                name: 'Energy Collector',
                produces: 'energy_units',
                rate: 5,
                requirements: { location: 'power_station', skill: { vrilManipulation: 10 } }
            }
        ];

        productionFacilities.forEach(facility => {
            this.gameState.resources.production.set(facility.id, facility);
        });
    }

    /**
     * Process player action
     */
    async processPlayerAction(player, action) {
        if (this.isProcessing) {
            return { success: false, message: 'Please wait for the current action to complete.' };
        }

        this.isProcessing = true;
        this.lastActionTime = Date.now();

        try {
            // Update game stats
            this.gameState.gameStats.actionsPerformed++;

            // Award experience for actions
            this.awardActionExperience(player, action);

            // Add to message history
            this.addToMessageHistory('player', player.name, action);
            
            // Process the action through AI
            const context = this.buildActionContext(player, action);
            const response = await this.aiManager.generateResponse(action, player, context);
            
            // Parse response for game elements
            const gameEvents = this.parseResponseForGameElements(response);
            
            // Apply game events
            await this.applyGameEvents(gameEvents, player);
            
            // Add DM response to history
            this.addToMessageHistory('dm', 'The Eternal Keeper', response);
            
            // Check for quest updates
            this.updateQuests(gameEvents, player);
            
            // Trigger random events
            this.checkForRandomEvents();
            
            // Update play time
            this.updatePlayTime();
            
            return {
                success: true,
                response: response,
                gameEvents: gameEvents
            };
            
        } catch (error) {
            console.error('Error processing player action:', error);
            return {
                success: false,
                message: 'The connection to the Akashic Records was interrupted. Please try again.'
            };
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * Build context for AI action processing
     */
    buildActionContext(player, action) {
        const currentLocation = CONFIG.locations[this.gameState.location];
        const characterLore = this.characterManager.getCharacterLore(player.class);
        const contextualLore = getContextualLore(this.gameState.location, player);
        
        return {
            location: currentLocation,
            character: player,
            characterLore: characterLore,
            worldState: this.gameState.worldState,
            recentHistory: this.getRecentHistory(5),
            activeQuests: this.gameState.worldState.questsActive,
            nearbyNPCs: this.getNearbyNPCs(),
            availableItems: this.getAvailableItems(),
            gameStats: { ...this.gameState.gameStats, chapter: this.gameState.chapter },
            contextualLore: contextualLore
        };
    }

    /**
     * Parse AI response for game elements
     */
    parseResponseForGameElements(response) {
        const events = {
            locationChange: null,
            itemsFound: [],
            npcsEncountered: [],
            questUpdates: [],
            statChanges: {},
            discoveries: [],
            combatEvents: [],
            dialogueEvents: []
        };

        // Extract location changes
        const locationMatch = response.match(/You (?:enter|arrive at|reach|travel to|visit) (?:the )?(.+?)[\.\,\!]/i);
        if (locationMatch) {
            const newLocation = locationMatch[1].trim();
            // Check both exact match and partial match
            const matchedLocation = Object.keys(CONFIG.locations).find(loc =>
                loc.toLowerCase().includes(newLocation.toLowerCase()) ||
                newLocation.toLowerCase().includes(loc.toLowerCase())
            );
            if (matchedLocation) {
                events.locationChange = matchedLocation;
            }
        }

        // Also check for location mentions in general
        Object.keys(CONFIG.locations).forEach(location => {
            if (response.toLowerCase().includes(location.toLowerCase())) {
                events.locationChange = location;
            }
        });

        // Extract items found
        const itemMatches = response.matchAll(/You (?:find|discover|receive|obtain) (?:a |an |the )?(.+?)[\.\,\!]/gi);
        for (const match of itemMatches) {
            events.itemsFound.push(match[1].trim());
        }

        // Extract NPC encounters
        const npcMatches = response.matchAll(/(?:meet|encounter|see|speak with|talk to|greet) (?:a |an |the )?(.+?) (?:who|that|says|tells|explains)/gi);
        for (const match of npcMatches) {
            events.npcsEncountered.push(match[1].trim());
        }

        // Also check for specific NPC names
        const npcNames = ['Zephyr', 'Thoth', 'Adama', 'Kael', 'Guardian', 'Elder', 'Keeper', 'Engineer'];
        npcNames.forEach(name => {
            if (response.toLowerCase().includes(name.toLowerCase())) {
                events.npcsEncountered.push(name);
            }
        });

        // Extract dice rolls and outcomes
        const rollMatches = response.matchAll(/\[Roll: ?(\d+)\]/gi);
        for (const match of rollMatches) {
            const roll = parseInt(match[1]);
            events.combatEvents.push({ type: 'dice_roll', value: roll });
        }

        // Extract stat changes (health, vril, etc.)
        const healthMatch = response.match(/(?:lose|gain|restore) (\d+) (?:health|life|vitality)/i);
        if (healthMatch) {
            const amount = parseInt(healthMatch[1]);
            const isGain = response.includes('gain') || response.includes('restore');
            events.statChanges.health = isGain ? amount : -amount;
        }

        const vrilMatch = response.match(/(?:lose|gain|channel) (\d+) (?:vril|energy)/i);
        if (vrilMatch) {
            const amount = parseInt(vrilMatch[1]);
            const isGain = response.includes('gain') || response.includes('channel');
            events.statChanges.vril = isGain ? amount : -amount;
        }

        return events;
    }

    /**
     * Apply game events to the world state
     */
    async applyGameEvents(events, player) {
        // Handle location changes
        if (events.locationChange) {
            this.changeLocation(events.locationChange);
        }

        // Handle items found
        events.itemsFound.forEach(item => {
            this.addItemToInventory(player, item);
        });

        // Handle NPC encounters
        events.npcsEncountered.forEach(npcName => {
            this.recordNPCEncounter(npcName);
        });

        // Handle stat changes
        if (Object.keys(events.statChanges).length > 0) {
            this.characterManager.updateCharacterStats(player, events.statChanges);
        }

        // Handle discoveries
        events.discoveries.forEach(discovery => {
            this.recordDiscovery(discovery);
        });

        // Emit events for UI updates
        this.emit('gameStateChanged', {
            gameState: this.gameState,
            events: events
        });
    }

    /**
     * Change current location
     */
    changeLocation(newLocation) {
        const oldLocation = this.gameState.location;
        this.gameState.location = newLocation;
        
        // Add to discovered locations
        if (!this.gameState.worldState.discovered.includes(newLocation)) {
            this.gameState.worldState.discovered.push(newLocation);
            this.gameState.gameStats.locationsVisited++;
        }
        
        this.emit('locationChanged', {
            from: oldLocation,
            to: newLocation
        });
    }

    /**
     * Add item to player inventory
     */
    addItemToInventory(player, itemName) {
        const playerId = player.name;
        if (!this.gameState.inventory.has(playerId)) {
            this.gameState.inventory.set(playerId, []);
        }
        
        const playerInventory = this.gameState.inventory.get(playerId);
        if (playerInventory.length < 12) { // Max inventory size
            playerInventory.push({
                name: itemName,
                type: this.determineItemType(itemName),
                acquired: Date.now()
            });
            
            this.emit('itemAdded', {
                player: playerId,
                item: itemName
            });
        }
    }

    /**
     * Determine item type based on name
     */
    determineItemType(itemName) {
        const name = itemName.toLowerCase();
        
        if (name.includes('crystal')) return 'crystal';
        if (name.includes('scroll') || name.includes('tome')) return 'knowledge';
        if (name.includes('key') || name.includes('device')) return 'tool';
        if (name.includes('armor') || name.includes('shield')) return 'armor';
        if (name.includes('weapon') || name.includes('blade')) return 'weapon';
        if (name.includes('potion') || name.includes('elixir')) return 'consumable';
        
        return 'misc';
    }

    /**
     * Record NPC encounter
     */
    recordNPCEncounter(npcName) {
        if (!this.gameState.worldState.npcsmet.includes(npcName)) {
            this.gameState.worldState.npcsmet.push(npcName);
            this.gameState.gameStats.npcsEncountered++;
        }
    }

    /**
     * Update quest progress
     */
    updateQuests(events, player) {
        // Check active quests for completion criteria
        this.gameState.worldState.questsActive.forEach(quest => {
            if (quest.status === 'active') {
                // Check if any objectives are completed by recent events
                quest.objectives.forEach((objective, index) => {
                    if (!quest.completedObjectives) quest.completedObjectives = [];
                    
                    if (!quest.completedObjectives.includes(index)) {
                        if (this.checkObjectiveCompletion(objective, events, player)) {
                            quest.completedObjectives.push(index);
                        }
                    }
                });
                
                // Check if quest is complete
                if (quest.completedObjectives && 
                    quest.completedObjectives.length === quest.objectives.length) {
                    this.completeQuest(quest, player);
                }
            }
        });
    }

    /**
     * Check if a quest objective is completed
     */
    checkObjectiveCompletion(objective, events, player) {
        const obj = objective.toLowerCase();
        const recentMessages = this.getRecentHistory(3);
        const recentText = recentMessages.map(m => m.content.toLowerCase()).join(' ');

        // Location-based objectives
        if ((obj.includes('enter') || obj.includes('visit') || obj.includes('travel to')) && events.locationChange) {
            return obj.includes(events.locationChange.toLowerCase()) ||
                   recentText.includes(events.locationChange.toLowerCase());
        }

        // NPC interaction objectives
        if (obj.includes('speak with') || obj.includes('talk to') || obj.includes('meet')) {
            const npcNames = ['zephyr', 'thoth', 'adama', 'guardian', 'elder', 'keeper'];
            return events.npcsEncountered.some(npc => obj.includes(npc.toLowerCase())) ||
                   npcNames.some(npc => obj.includes(npc) && recentText.includes(npc));
        }

        // Item collection objectives
        if (obj.includes('find') || obj.includes('collect') || obj.includes('acquire') || obj.includes('obtain')) {
            return events.itemsFound.some(item => obj.includes(item.toLowerCase())) ||
                   recentText.includes('crystal') && obj.includes('crystal') ||
                   recentText.includes('artifact') && obj.includes('artifact');
        }

        // Action-based objectives
        if (obj.includes('examine') || obj.includes('look at')) {
            return recentText.includes('examine') || recentText.includes('look') || recentText.includes('observe');
        }

        // Learning objectives
        if (obj.includes('learn') || obj.includes('understand')) {
            return recentText.includes('learn') || recentText.includes('understand') ||
                   recentText.includes('knowledge') || recentText.includes('wisdom');
        }

        // Ritual/action objectives
        if (obj.includes('ritual') || obj.includes('attunement') || obj.includes('channel')) {
            return recentText.includes('ritual') || recentText.includes('channel') ||
                   recentText.includes('energy') || recentText.includes('vril');
        }

        // Communication objectives
        if (obj.includes('telepathic') || obj.includes('communicate')) {
            return recentText.includes('telepathic') || recentText.includes('mind') ||
                   recentText.includes('communicate') || recentText.includes('thoughts');
        }

        // Generic completion check - if the objective keywords appear in recent messages
        const objectiveWords = obj.split(' ').filter(word => word.length > 3);
        const matchCount = objectiveWords.filter(word => recentText.includes(word)).length;

        return matchCount >= Math.ceil(objectiveWords.length * 0.6); // 60% word match
    }

    /**
     * Complete a quest
     */
    completeQuest(quest, player) {
        quest.status = 'completed';
        quest.completedAt = Date.now();

        // Move to completed quests
        this.gameState.worldState.questsCompleted.push(quest);
        this.gameState.worldState.questsActive = this.gameState.worldState.questsActive.filter(q => q.id !== quest.id);

        // Award rewards
        if (quest.rewards) {
            if (quest.rewards.experience) {
                this.characterManager.awardExperience(player, quest.rewards.experience);
            }

            if (quest.rewards.items) {
                quest.rewards.items.forEach(item => {
                    this.addItemToInventory(player, item);
                });
            }
        }

        this.gameState.gameStats.questsCompleted++;

        // Check for chapter progression
        if (quest.chapter) {
            this.gameState.chapter = Math.max(this.gameState.chapter, quest.chapter + 1);
        }

        // Activate next quest in chain
        if (quest.nextQuest) {
            this.activateNextQuest(quest.nextQuest);
        }

        // Check for game completion
        if (quest.isEndGame) {
            this.triggerGameCompletion(player);
        }

        this.emit('questCompleted', {
            quest: quest,
            player: player,
            isEndGame: quest.isEndGame
        });
    }

    /**
     * Activate the next quest in the chain
     */
    activateNextQuest(questId) {
        const questChain = this.gameState.worldState.questChain;
        const nextQuest = questChain.find(q => q.id === questId);

        if (nextQuest && nextQuest.status === 'locked') {
            nextQuest.status = 'active';
            this.gameState.worldState.questsActive.push(nextQuest);

            this.emit('questActivated', {
                quest: nextQuest
            });
        }
    }

    /**
     * Trigger game completion sequence
     */
    triggerGameCompletion(player) {
        this.gameState.gameCompleted = true;
        this.gameState.completionTime = Date.now();

        // Calculate final score
        const finalScore = this.calculateFinalScore(player);
        this.gameState.finalScore = finalScore;

        // Unlock special ending content
        this.unlockEndingContent(player);

        this.emit('gameCompleted', {
            player: player,
            finalScore: finalScore,
            completionTime: this.gameState.completionTime,
            playTime: this.gameState.gameStats.playTime
        });
    }

    /**
     * Calculate final score based on player achievements
     */
    calculateFinalScore(player) {
        let score = 0;

        // Base score from level and experience
        score += player.level * 1000;
        score += player.experience;

        // Bonus for quests completed
        score += this.gameState.gameStats.questsCompleted * 500;

        // Bonus for locations discovered
        score += this.gameState.worldState.discovered.length * 200;

        // Bonus for NPCs encountered
        score += this.gameState.gameStats.npcsEncountered * 100;

        // Time bonus (faster completion = higher score)
        const playTimeHours = this.gameState.gameStats.playTime / (1000 * 60 * 60);
        const timeBonus = Math.max(0, 10000 - (playTimeHours * 100));
        score += timeBonus;

        return Math.round(score);
    }

    /**
     * Unlock special ending content
     */
    unlockEndingContent(player) {
        // Add special ending items
        this.addItemToInventory(player, 'Certificate of Ascension');
        this.addItemToInventory(player, 'Eternal Flame');

        // Unlock all locations for exploration
        Object.keys(CONFIG.locations).forEach(location => {
            if (!this.gameState.worldState.discovered.includes(location)) {
                this.gameState.worldState.discovered.push(location);
            }
        });

        // Grant maximum stats
        Object.keys(player.stats).forEach(stat => {
            player.stats[stat] = Math.max(player.stats[stat], 25);
        });

        // Full health and vril restoration
        player.health = player.maxHealth;
        player.vril = player.maxVril;
    }

    /**
     * Check for random events
     */
    checkForRandomEvents() {
        if (Math.random() > 0.92) { // 8% chance
            setTimeout(() => {
                this.triggerRandomEvent();
            }, Math.random() * 10000 + 5000); // 5-15 seconds delay
        }
    }

    /**
     * Trigger a random environmental event
     */
    triggerRandomEvent() {
        const events = [
            "The crystalline walls pulse with renewed energy, casting dancing shadows.",
            "A distant harmonic tone echoes through the caverns, resonating in your bones.",
            "The air shimmers as reality briefly fluctuates around you.",
            "You sense an ancient presence observing from the ethereal realm.",
            "The vril currents suddenly intensify, making your skin tingle.",
            "Whispers in an unknown language drift on the underground breeze.",
            "A faint aurora of colored light plays across the cavern ceiling.",
            "The ground trembles slightly as massive machinery operates far below."
        ];
        
        const event = events[Math.floor(Math.random() * events.length)];
        
        this.addToMessageHistory('environment', 'The Inner Realm', event);
        
        this.emit('randomEvent', {
            event: event,
            timestamp: Date.now()
        });
    }

    /**
     * Add message to history
     */
    addToMessageHistory(type, author, content) {
        const message = {
            type,
            author,
            content,
            timestamp: Date.now(),
            location: this.gameState.location
        };
        
        this.gameState.messageHistory.push(message);
        
        // Limit history size
        if (this.gameState.messageHistory.length > CONFIG.game.maxMessages) {
            this.gameState.messageHistory.shift();
        }
    }

    /**
     * Get recent message history
     */
    getRecentHistory(count = 5) {
        return this.gameState.messageHistory.slice(-count);
    }

    /**
     * Get NPCs in current location
     */
    getNearbyNPCs() {
        return Array.from(this.gameState.npcs.values()).filter(npc => 
            npc.location === this.gameState.location
        );
    }

    /**
     * Get available items in current location
     */
    getAvailableItems() {
        const location = CONFIG.locations[this.gameState.location];
        return location ? location.items || [] : [];
    }

    /**
     * Update play time
     */
    updatePlayTime() {
        this.gameState.gameStats.playTime = Date.now() - this.gameState.gameStats.startTime;
    }

    /**
     * Save game state manually
     */
    async saveGame() {
        try {
            await this.saveManager.saveGame({
                gameState: this.gameState,
                timestamp: Date.now(),
                version: CONFIG.game.version
            });
            return true;
        } catch (error) {
            console.error('Manual save failed:', error);
            throw error;
        }
    }

    /**
     * Auto-save game state
     */
    async autoSave() {
        try {
            await this.saveManager.saveGame({
                gameState: this.gameState,
                timestamp: Date.now(),
                version: CONFIG.game.version
            });
        } catch (error) {
            console.warn('Auto-save failed:', error);
        }
    }

    /**
     * Event system methods
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }

    /**
     * Get current game state
     */
    getGameState() {
        return { ...this.gameState };
    }

    /**
     * Award experience for player actions
     */
    awardActionExperience(player, action) {
        let experienceGained = CONFIG.mechanics.experience.basePerAction;

        // Bonus for creative/detailed actions
        if (action.length > 50) {
            experienceGained += CONFIG.mechanics.experience.bonusForCreativity;
        }

        // Bonus for roleplay elements
        if (action.toLowerCase().includes('as a ') || action.toLowerCase().includes('my character')) {
            experienceGained += CONFIG.mechanics.experience.bonusForRoleplay;
        }

        // Bonus for using class abilities
        const classConfig = CONFIG.characterClasses[player.class];
        if (classConfig && classConfig.abilities.some(ability =>
            action.toLowerCase().includes(ability.toLowerCase()))) {
            experienceGained += 15;
        }

        // Award the experience
        if (experienceGained > CONFIG.mechanics.experience.basePerAction) {
            this.characterManager.awardExperience(player, experienceGained);
        }
    }

    /**
     * Cleanup resources
     */
    destroy() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
        }

        this.eventListeners.clear();
    }
}
