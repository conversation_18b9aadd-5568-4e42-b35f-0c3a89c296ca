/**
 * AGARTHA RPG - MULTIPLAYER UI COMPONENTS
 * UI components for multiplayer functionality
 */

export class MultiplayerUI {
    constructor(multiplayerManager, uiManager) {
        this.multiplayerManager = multiplayerManager;
        this.uiManager = uiManager;
        
        this.isMultiplayerModalOpen = false;
    }

    /**
     * Show multiplayer setup modal
     */
    showMultiplayerSetup() {
        const modalContent = `
            <div class="multiplayer-setup">
                <h3>🎮 Multiplayer Setup</h3>
                <p>Choose how you want to play with others:</p>
                
                <div class="multiplayer-options">
                    <div class="option-card" id="hostGameOption">
                        <div class="option-icon">👑</div>
                        <h4>Host Game</h4>
                        <p>Run the AI on your device and let others join</p>
                        <ul>
                            <li>✅ You download and run the AI model</li>
                            <li>✅ Others join without downloading anything</li>
                            <li>✅ You control the game world</li>
                        </ul>
                        <button class="btn-primary" onclick="window.multiplayerUI.showHostSetup()">
                            Host Game
                        </button>
                    </div>
                    
                    <div class="option-card" id="joinGameOption">
                        <div class="option-icon">👤</div>
                        <h4>Join Game</h4>
                        <p>Join someone else's hosted game</p>
                        <ul>
                            <li>✅ No AI model download required</li>
                            <li>✅ Instant join with room code</li>
                            <li>✅ Share the adventure with friends</li>
                        </ul>
                        <button class="btn-secondary" onclick="window.multiplayerUI.showJoinSetup()">
                            Join Game
                        </button>
                    </div>
                </div>
                
                <div class="multiplayer-info">
                    <h4>📋 How It Works</h4>
                    <p><strong>Host:</strong> Downloads AI model, runs game, shares room code</p>
                    <p><strong>Players:</strong> Use room code to join, no downloads needed</p>
                    <p><strong>Server:</strong> Coordinates game state between all players</p>
                </div>
            </div>
        `;

        this.uiManager.showModal('Multiplayer Setup', modalContent, {
            showCloseButton: true,
            className: 'multiplayer-modal'
        });

        this.isMultiplayerModalOpen = true;
    }

    /**
     * Show host game setup
     */
    showHostSetup() {
        const modalContent = `
            <div class="host-setup">
                <h3>👑 Host Multiplayer Game</h3>
                
                <div class="setup-form">
                    <div class="form-group">
                        <label for="serverUrl">Server URL:</label>
                        <input type="text" id="serverUrl" value="ws://localhost:3000" 
                               placeholder="ws://your-server.com:3000">
                        <small>Enter your multiplayer server URL</small>
                    </div>
                    
                    <div class="form-group">
                        <label>AI Model Selection:</label>
                        <div id="hostModelSelection">
                            <!-- Model options will be populated here -->
                        </div>
                        <small>Choose the AI model that will run on your device</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="hostPlayerName">Your Character Name:</label>
                        <input type="text" id="hostPlayerName" placeholder="Enter your character name">
                    </div>
                    
                    <div class="form-group">
                        <label for="hostPlayerClass">Character Class:</label>
                        <select id="hostPlayerClass">
                            <option value="mystic">🔮 Mystic - Master of ancient wisdom</option>
                            <option value="explorer">🗺️ Explorer - Brave adventurer</option>
                            <option value="scholar">📚 Scholar - Keeper of knowledge</option>
                            <option value="guardian">⚔️ Guardian - Protector of secrets</option>
                        </select>
                    </div>
                </div>
                
                <div class="setup-actions">
                    <button class="btn-secondary" onclick="window.multiplayerUI.showMultiplayerSetup()">
                        ← Back
                    </button>
                    <button class="btn-primary" onclick="window.multiplayerUI.startHosting()">
                        🚀 Start Hosting
                    </button>
                </div>
                
                <div class="host-info">
                    <h4>⚠️ Host Requirements</h4>
                    <ul>
                        <li>Stable internet connection</li>
                        <li>Sufficient RAM for AI model (4-8GB recommended)</li>
                        <li>Modern browser with WebGPU support</li>
                        <li>Keep browser tab open during game</li>
                    </ul>
                </div>
            </div>
        `;

        this.uiManager.showModal('Host Game', modalContent, {
            showCloseButton: true,
            className: 'multiplayer-modal'
        });

        // Populate model options
        this.populateHostModelOptions();
    }

    /**
     * Show join game setup
     */
    showJoinSetup() {
        const modalContent = `
            <div class="join-setup">
                <h3>👤 Join Multiplayer Game</h3>
                
                <div class="setup-form">
                    <div class="form-group">
                        <label for="joinServerUrl">Server URL:</label>
                        <input type="text" id="joinServerUrl" value="ws://localhost:3000" 
                               placeholder="ws://your-server.com:3000">
                        <small>Enter the multiplayer server URL</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="roomCode">Room Code:</label>
                        <input type="text" id="roomCode" placeholder="Enter room code (e.g., ROOM_ABC123)" 
                               style="text-transform: uppercase;">
                        <small>Get this code from the game host</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="joinPlayerName">Your Character Name:</label>
                        <input type="text" id="joinPlayerName" placeholder="Enter your character name">
                    </div>
                    
                    <div class="form-group">
                        <label for="joinPlayerClass">Character Class:</label>
                        <select id="joinPlayerClass">
                            <option value="mystic">🔮 Mystic - Master of ancient wisdom</option>
                            <option value="explorer">🗺️ Explorer - Brave adventurer</option>
                            <option value="scholar">📚 Scholar - Keeper of knowledge</option>
                            <option value="guardian">⚔️ Guardian - Protector of secrets</option>
                        </select>
                    </div>
                </div>
                
                <div class="setup-actions">
                    <button class="btn-secondary" onclick="window.multiplayerUI.showMultiplayerSetup()">
                        ← Back
                    </button>
                    <button class="btn-primary" onclick="window.multiplayerUI.joinGame()">
                        🎮 Join Game
                    </button>
                </div>
                
                <div class="join-info">
                    <h4>✨ Player Benefits</h4>
                    <ul>
                        <li>No AI model download required</li>
                        <li>Instant game access</li>
                        <li>Shared adventure with friends</li>
                        <li>Real-time synchronized gameplay</li>
                    </ul>
                </div>
            </div>
        `;

        this.uiManager.showModal('Join Game', modalContent, {
            showCloseButton: true,
            className: 'multiplayer-modal'
        });

        // Auto-populate from URL parameters if available
        this.checkForJoinParameters();
    }

    /**
     * Populate model options for hosting
     */
    populateHostModelOptions() {
        const container = document.getElementById('hostModelSelection');
        if (!container) return;

        // Get available models from config
        const models = window.CONFIG?.models || {};
        
        let modelsHTML = '';
        Object.entries(models).forEach(([modelId, config]) => {
            modelsHTML += `
                <div class="model-option">
                    <input type="radio" name="hostModel" value="${modelId}" id="host_${modelId}">
                    <label for="host_${modelId}">
                        <strong>${config.name}</strong>
                        <span class="model-size">${config.size}</span>
                        <div class="model-description">${config.description}</div>
                    </label>
                </div>
            `;
        });

        container.innerHTML = modelsHTML;
    }

    /**
     * Check for join parameters in URL
     */
    checkForJoinParameters() {
        const urlParams = new URLSearchParams(window.location.search);
        const roomCode = urlParams.get('room');
        const serverUrl = urlParams.get('server');

        if (roomCode) {
            const roomInput = document.getElementById('roomCode');
            if (roomInput) roomInput.value = roomCode;
        }

        if (serverUrl) {
            const serverInput = document.getElementById('joinServerUrl');
            if (serverInput) serverInput.value = decodeURIComponent(serverUrl);
        }
    }

    /**
     * Start hosting a game
     */
    async startHosting() {
        const serverUrl = document.getElementById('serverUrl')?.value;
        const playerName = document.getElementById('hostPlayerName')?.value;
        const playerClass = document.getElementById('hostPlayerClass')?.value;
        const selectedModel = document.querySelector('input[name="hostModel"]:checked')?.value;

        // Validation
        if (!serverUrl || !playerName || !playerClass || !selectedModel) {
            this.uiManager.showNotification('Please fill in all fields', 'error');
            return;
        }

        try {
            // Show loading
            this.uiManager.showNotification('Setting up multiplayer game...', 'info');

            // Create player data
            const playerData = {
                name: playerName,
                class: playerClass,
                id: this.multiplayerManager.generatePlayerId()
            };

            // Start hosting
            const result = await this.multiplayerManager.hostGame(serverUrl, playerData, selectedModel);

            if (result.success) {
                // Close modal
                this.uiManager.hideModal();
                
                // Show success with share info
                this.showHostSuccessInfo(result);
                
                // Initialize AI with selected model
                await this.initializeHostAI(selectedModel, playerData);
                
            } else {
                this.uiManager.showNotification(`Failed to host game: ${result.error}`, 'error');
            }

        } catch (error) {
            console.error('Error starting host:', error);
            this.uiManager.showNotification('Failed to start hosting', 'error');
        }
    }

    /**
     * Join an existing game
     */
    async joinGame() {
        const serverUrl = document.getElementById('joinServerUrl')?.value;
        const roomCode = document.getElementById('roomCode')?.value.toUpperCase();
        const playerName = document.getElementById('joinPlayerName')?.value;
        const playerClass = document.getElementById('joinPlayerClass')?.value;

        // Validation
        if (!serverUrl || !roomCode || !playerName || !playerClass) {
            this.uiManager.showNotification('Please fill in all fields', 'error');
            return;
        }

        try {
            // Show loading
            this.uiManager.showNotification('Joining multiplayer game...', 'info');

            // Create player data
            const playerData = {
                name: playerName,
                class: playerClass,
                id: this.multiplayerManager.generatePlayerId()
            };

            // Join game
            const result = await this.multiplayerManager.joinGame(serverUrl, roomCode, playerData);

            if (result.success) {
                // Close modal
                this.uiManager.hideModal();
                
                // Show success
                this.uiManager.showNotification('Successfully joined multiplayer game!', 'success');
                
                // Switch to game view
                this.switchToGameView(playerData, null, false);
                
            } else {
                this.uiManager.showNotification(`Failed to join game: ${result.error}`, 'error');
            }

        } catch (error) {
            console.error('Error joining game:', error);
            this.uiManager.showNotification('Failed to join game', 'error');
        }
    }

    /**
     * Show host success information
     */
    showHostSuccessInfo(result) {
        const modalContent = `
            <div class="host-success">
                <h3>🎉 Game Hosted Successfully!</h3>
                
                <div class="room-info">
                    <h4>📋 Room Information</h4>
                    <div class="info-item">
                        <label>Room Code:</label>
                        <div class="room-code">${result.roomId}</div>
                        <button onclick="navigator.clipboard.writeText('${result.roomId}')" class="copy-btn">📋 Copy</button>
                    </div>
                    
                    <div class="info-item">
                        <label>Share URL:</label>
                        <div class="share-url">${result.shareUrl}</div>
                        <button onclick="navigator.clipboard.writeText('${result.shareUrl}')" class="copy-btn">📋 Copy</button>
                    </div>
                </div>
                
                <div class="next-steps">
                    <h4>📢 Next Steps</h4>
                    <ol>
                        <li>Share the room code or URL with your friends</li>
                        <li>Wait for players to join</li>
                        <li>Start your adventure together!</li>
                    </ol>
                </div>
                
                <div class="host-controls">
                    <button class="btn-primary" onclick="window.multiplayerUI.continueToGame()">
                        🚀 Continue to Game
                    </button>
                </div>
            </div>
        `;

        this.uiManager.showModal('Game Hosted!', modalContent, {
            showCloseButton: false,
            className: 'success-modal'
        });
    }

    /**
     * Initialize AI for host
     */
    async initializeHostAI(modelId, playerData) {
        try {
            // This would integrate with your existing AI initialization
            // For now, we'll assume the AI manager is available globally
            if (window.agarthaApp && window.agarthaApp.aiManager) {
                await window.agarthaApp.aiManager.initializeEngine(modelId);
                console.log('✅ Host AI initialized');
            }
        } catch (error) {
            console.error('Failed to initialize host AI:', error);
            this.uiManager.showNotification('Warning: AI initialization failed', 'warning');
        }
    }

    /**
     * Continue to game after hosting setup
     */
    continueToGame() {
        this.uiManager.hideModal();
        // Switch to game interface
        this.switchToGameView(null, null, true);
    }

    /**
     * Switch to game view
     */
    switchToGameView(playerData, aiModel, isHost) {
        // Hide setup screens and show game interface
        const setupScreens = document.querySelectorAll('.setup-screen');
        setupScreens.forEach(screen => screen.style.display = 'none');

        const gameScreen = document.getElementById('gameScreen');
        if (gameScreen) {
            gameScreen.style.display = 'block';
        }

        // Add multiplayer UI elements to game screen
        this.addMultiplayerUIToGame();
    }

    /**
     * Add multiplayer UI elements to the game screen
     */
    addMultiplayerUIToGame() {
        // Add multiplayer info panel to sidebar
        const sidebar = document.querySelector('.game-sidebar');
        if (sidebar) {
            const multiplayerPanel = document.createElement('div');
            multiplayerPanel.className = 'multiplayer-panel';
            multiplayerPanel.innerHTML = `
                <div id="multiplayerRoomInfo" class="room-info-panel">
                    <!-- Room info will be populated by MultiplayerManager -->
                </div>
                <div id="multiplayerPlayersList" class="players-list-panel">
                    <!-- Players list will be populated by MultiplayerManager -->
                </div>
            `;
            
            sidebar.appendChild(multiplayerPanel);
        }

        // Update the multiplayer manager's UI
        if (this.multiplayerManager) {
            this.multiplayerManager.updateMultiplayerUI();
        }
    }

    /**
     * Show multiplayer button in main menu
     */
    addMultiplayerButton() {
        const buttonContainer = document.querySelector('.game-modes') || document.querySelector('.main-actions');
        if (buttonContainer) {
            const multiplayerBtn = document.createElement('button');
            multiplayerBtn.className = 'btn-secondary multiplayer-btn';
            multiplayerBtn.innerHTML = '🎮 Multiplayer';
            multiplayerBtn.onclick = () => this.showMultiplayerSetup();
            
            buttonContainer.appendChild(multiplayerBtn);
        }
    }
}

// Make available globally for onclick handlers
window.multiplayerUI = null;
