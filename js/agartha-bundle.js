/**
 * <PERSON><PERSON><PERSON><PERSON> RPG - BUNDLED VERSION FOR HOSTING COMPATIBILITY
 * All modules combined into a single file for easy deployment
 */

// ===== CONFIGURATION =====
const CONFIG = {
    game: {
        title: "Agartha: The Lost City of Light",
        version: "2.0.0",
        maxPlayers: 12,
        maxMessages: 150,
        saveInterval: 45000,
        autoSaveEnabled: true,
        debugMode: false
    },
    
    // AI Model configurations for WebLLM + Hugging Face
    // Full range from tiny to powerful models
    models: {
        'demo-mode': {
            name: 'Demo Mode',
            displayName: '🎭 Demo Mode (Always Works)',
            description: 'Instant testing with pre-written responses - no download required, guaranteed compatibility!',
            contextLength: 4096,
            temperature: 0.8,
            topP: 0.9,
            maxTokens: 250,
            size: '0MB',
            params: 'Demo',
            speed: 'Instant',
            isDemo: true,
            recommended: true,
            huggingFaceRepo: 'Built-in demo responses'
        },
        // === TEST THESE MODELS (Real WebLLM IDs) ===
        'TinyLlama-1.1B-Chat-v1.0-q4f16_1-MLC': {
            name: 'TinyLlama 1.1B',
            displayName: '🐭 TinyLlama 1.1B (Test Tiny)',
            description: 'Ultra-small model - try this first! If it works, perfect for bundling.',
            contextLength: 2048,
            temperature: 0.8,
            topP: 0.9,
            maxTokens: 200,
            size: '637MB',
            params: '1.1B',
            speed: 'Very Fast',
            huggingFaceRepo: 'TinyLlama/TinyLlama-1.1B-Chat-v1.0'
        },
        'Qwen2-0.5B-Instruct-q4f16_1-MLC': {
            name: 'Qwen2 0.5B',
            displayName: '⚡ Qwen2 0.5B (Test Super Tiny)',
            description: 'Even smaller! If this works, it\'s the best choice for bundling.',
            contextLength: 2048,
            temperature: 0.8,
            topP: 0.9,
            maxTokens: 150,
            size: '300MB',
            params: '0.5B',
            speed: 'Lightning',
            huggingFaceRepo: 'Qwen/Qwen2-0.5B-Instruct'
        },
        'Phi-3-mini-4k-instruct-q4f16_1-MLC': {
            name: 'Phi-3 Mini',
            displayName: '⭐ Phi-3 Mini (Test Regular)',
            description: 'Good quality model - test if you want better responses than tiny models.',
            contextLength: 4096,
            temperature: 0.8,
            topP: 0.95,
            maxTokens: 300,
            size: '2.2GB',
            params: '3.8B',
            speed: 'Good',
            huggingFaceRepo: 'microsoft/Phi-3-mini-4k-instruct'
        },
        'gemma-2-2b-it-q4f16_1-MLC': {
            name: 'Gemma 2 2B',
            displayName: '💎 Gemma 2 2B (Test Creative)',
            description: 'Creative model - test for storytelling quality comparison.',
            contextLength: 8192,
            temperature: 0.85,
            topP: 0.9,
            maxTokens: 350,
            size: '1.6GB',
            params: '2B',
            speed: 'Good',
            huggingFaceRepo: 'google/gemma-2-2b-it'
        },
        'Qwen2-7B-Instruct-q4f16_1-MLC': {
            name: 'Qwen2 7B',
            displayName: '🌟 Qwen2 7B (Advanced)',
            description: 'Alibaba\'s powerful model with excellent creative writing abilities',
            contextLength: 8192,
            temperature: 0.85,
            topP: 0.9,
            maxTokens: 500,
            size: '4.2GB',
            params: '7B',
            speed: 'Slower',
            huggingFaceRepo: 'Qwen/Qwen2-7B-Instruct'
        },
        'demo-mode': {
            name: 'Demo Mode',
            displayName: '🎭 Demo Mode (No Download)',
            description: 'Test the game with pre-written responses - no AI model download required',
            contextLength: 4096,
            temperature: 0.8,
            topP: 0.9,
            maxTokens: 250,
            size: '0MB',
            params: 'Demo',
            speed: 'Instant',
            isDemo: true,
            huggingFaceRepo: 'Built-in demo responses'
        }
    },
    
    characterClasses: {
        'Crystal Keeper': {
            name: 'Crystal Keeper',
            title: 'Master of Ancient Energies',
            description: 'Harnesses the power of crystalline matrices and sacred geometries',
            primaryStat: 'vril',
            baseStats: { vril: 18, wisdom: 16, resonance: 15, vitality: 12, agility: 10, strength: 9 },
            startingItems: ['Crystal Focus', 'Energy Amplifier', 'Meditation Beads'],
            abilities: ['Crystal Resonance', 'Energy Channeling', 'Harmonic Healing'],
            lore: 'Descendants of the first Agarthans who learned to commune with the living crystals that power the inner realm.'
        },
        'Vril Engineer': {
            name: 'Vril Engineer',
            title: 'Architect of Energy',
            description: 'Masters the technological applications of Vril energy',
            primaryStat: 'technology',
            baseStats: { technology: 18, vril: 15, intelligence: 16, vitality: 11, agility: 10, strength: 10 },
            startingItems: ['Vril Conductor', 'Energy Scanner', 'Technical Manual'],
            abilities: ['Energy Manipulation', 'Tech Interface', 'System Override'],
            lore: 'Brilliant minds who bridge ancient Vril wisdom with cutting-edge technology to maintain Agartha\'s infrastructure.'
        },
        'Lemurian Scholar': {
            name: 'Lemurian Scholar',
            title: 'Keeper of Ancient Wisdom',
            description: 'Preserves and interprets the knowledge of lost civilizations',
            primaryStat: 'wisdom',
            baseStats: { wisdom: 18, intelligence: 16, telepathy: 15, vitality: 10, agility: 9, strength: 8 },
            startingItems: ['Ancient Codex', 'Memory Crystal', 'Scholar\'s Robes'],
            abilities: ['Ancient Knowledge', 'Telepathic Link', 'Lore Mastery'],
            lore: 'Guardians of Lemurian traditions who can access the collective memory of their ancestors through meditation and crystal communion.'
        },
        'Atlantean Warrior': {
            name: 'Atlantean Warrior',
            title: 'Guardian of the Depths',
            description: 'Elite fighter trained in both physical and energy combat',
            primaryStat: 'strength',
            baseStats: { strength: 18, vitality: 16, agility: 15, vril: 12, intelligence: 10, wisdom: 9 },
            startingItems: ['Crystal Blade', 'Energy Shield', 'Warrior\'s Armor'],
            abilities: ['Combat Mastery', 'Energy Strike', 'Defensive Stance'],
            lore: 'Descendants of Atlantis\'s greatest warriors, trained in combat techniques that blend physical prowess with energy manipulation.'
        },
        'Inner Earth Scout': {
            name: 'Inner Earth Scout',
            title: 'Explorer of Hidden Realms',
            description: 'Expert navigator of Agartha\'s vast tunnel networks',
            primaryStat: 'agility',
            baseStats: { agility: 18, perception: 16, vitality: 15, intelligence: 12, strength: 10, wisdom: 9 },
            startingItems: ['Navigation Tools', 'Climbing Gear', 'Emergency Supplies'],
            abilities: ['Pathfinding', 'Stealth Movement', 'Danger Sense'],
            lore: 'Brave explorers who map the ever-changing passages of the inner Earth and serve as guides for travelers between realms.'
        },
        'Light Weaver': {
            name: 'Light Weaver',
            title: 'Manipulator of Consciousness',
            description: 'Channels the power of pure consciousness and light',
            primaryStat: 'telepathy',
            baseStats: { telepathy: 18, wisdom: 16, vril: 15, intelligence: 12, vitality: 10, agility: 9 },
            startingItems: ['Light Crystal', 'Consciousness Amplifier', 'Meditation Focus'],
            abilities: ['Light Manipulation', 'Mind Bridge', 'Consciousness Expansion'],
            lore: 'Rare individuals who can directly manipulate the fundamental forces of consciousness and light that permeate Agartha.'
        }
    }
};

// ===== UTILITY FUNCTIONS =====
function getModelConfig(modelId) {
    return CONFIG.models[modelId] || null;
}

function getCharacterClass(className) {
    return CONFIG.characterClasses[className] || null;
}

// ===== DEMO RESPONSES =====
const DEMO_RESPONSES = [
    "As you step through the Crystal Gates, the air shimmers with ancient energy. The Guardian Zephyr nods approvingly as you pass, and you find yourself in a vast underground plaza filled with beings from across the inner realms. Crystalline spires reach toward a glowing central sun, and the sound of flowing water echoes from hidden fountains. What draws your attention first?",

    "The mystical energies of Agartha respond to your presence. You notice a group of robed figures near an ornate fountain discussing something in hushed tones. To your left, a merchant's stall displays glowing crystals and ancient artifacts. Ahead, a grand staircase leads to what appears to be a library or hall of records. The very air seems alive with possibility.",

    "Your actions resonate through the crystalline structures around you. The ambient light shifts slightly, as if the city itself is acknowledging your presence. A gentle breeze carries the scent of exotic flowers and the faint sound of distant chanting. You feel a growing connection to the ancient wisdom that permeates this sacred place.",

    "The inhabitants of Agartha watch you with curious but welcoming eyes. Some appear human-like but with an otherworldly grace, while others seem to be beings of pure energy taking temporary form. A young woman with silver hair approaches and speaks in a melodious voice: 'Welcome, surface dweller. The Council of Elders has been expecting someone like you.'",

    "As you explore deeper into the realm, you discover that Agartha is far more vast than you initially imagined. Tunnels branch off in all directions, leading to different districts and settlements. The architecture seamlessly blends natural crystal formations with advanced technology that seems to run on pure consciousness and intention.",

    "Your journey takes an unexpected turn as you encounter ancient murals depicting the history of the three great civilizations: Lemuria, Atlantis, and Agartha itself. The images seem to move and shift as you watch, telling stories of great achievements, tragic falls, and the eternal hope for unity between the surface and inner worlds.",

    "The energy of the place fills you with a sense of purpose and destiny. You realize that your arrival here was no accident - you have been called to play a role in the great awakening that is to come. The question is: what path will you choose to walk in this mystical realm?"
];

// ===== AI MANAGER =====
class AIManager {
    constructor() {
        this.engine = null;
        this.currentModel = null;
        this.isReady = false;
        this.conversationHistory = [];
        this.demoResponseIndex = 0;
        this.isDemoMode = false;
    }

    async verifyModelAccess() {
        try {
            console.log('🔍 Verifying WebLLM model access...');

            // Test basic connectivity first
            console.log('🌐 Testing CDN connectivity...');
            const response = await fetch('https://esm.run/@mlc-ai/web-llm', { method: 'HEAD' });
            if (!response.ok) {
                throw new Error(`CDN not accessible: ${response.status}`);
            }
            console.log('✅ CDN is accessible');

            const webllm = await import("https://esm.run/@mlc-ai/web-llm");
            console.log('✅ WebLLM imported successfully');

            if (webllm.prebuiltAppConfig && webllm.prebuiltAppConfig.model_list) {
                const availableModels = webllm.prebuiltAppConfig.model_list;
                console.log('📋 Available WebLLM models:', availableModels.map(m => m.model_id));

                const configuredModels = Object.keys(CONFIG.models).filter(id => id !== 'demo-mode');
                const missingModels = configuredModels.filter(modelId =>
                    !availableModels.some(m => m.model_id === modelId)
                );

                if (missingModels.length > 0) {
                    console.warn('⚠️ Some configured models are not in WebLLM prebuilt list:', missingModels);
                    console.log('💡 Consider using Demo Mode for testing');
                } else {
                    console.log('✅ All configured models are available in WebLLM');
                }

                return { availableModels, configuredModels, missingModels };
            } else {
                console.warn('⚠️ WebLLM prebuilt config not found');
                return null;
            }

        } catch (error) {
            console.error('❌ Failed to verify model access:', error);
            console.log('💡 WebLLM may not be accessible. Demo Mode is still available.');
            return null;
        }
    }

    async initialize(modelId, options = {}) {
        try {
            console.log(`🤖 Initializing AI with model: ${modelId}`);

            const modelConfig = getModelConfig(modelId);
            if (!modelConfig) {
                throw new Error(`Unknown model: ${modelId}`);
            }

            // Check if this is demo mode
            if (modelConfig.isDemo) {
                console.log('🎭 Demo Mode activated - no download required!');
                this.isDemoMode = true;
                this.currentModel = modelId;
                this.isReady = true;

                // Simulate loading progress for demo
                if (options.onProgress) {
                    const steps = [
                        { progress: 0.2, text: 'Initializing demo mode...' },
                        { progress: 0.5, text: 'Loading demo responses...' },
                        { progress: 0.8, text: 'Preparing game environment...' },
                        { progress: 1.0, text: 'Demo mode ready!' }
                    ];

                    for (const step of steps) {
                        await new Promise(resolve => setTimeout(resolve, 300));
                        options.onProgress(step);
                    }
                }

                console.log('✅ Demo mode initialized successfully');
                return true;
            }

            // Regular WebLLM initialization
            console.log(`📦 This will download from Hugging Face: mlc-ai/${modelId}`);

            try {
                console.log('📥 Importing WebLLM from CDN...');
                const webllm = await import("https://esm.run/@mlc-ai/web-llm");
                console.log('✅ WebLLM imported successfully');
                console.log('📋 Available functions:', Object.keys(webllm));

                console.log(`🔄 Creating MLC Engine for ${modelId}...`);
                console.log(`📍 Model will be downloaded from: ${modelConfig.huggingFaceRepo}`);

                // Check if the model exists in WebLLM's prebuilt list
                if (webllm.prebuiltAppConfig && webllm.prebuiltAppConfig.model_list) {
                    const availableModels = webllm.prebuiltAppConfig.model_list.map(m => m.model_id);
                    console.log('📋 Total available WebLLM models:', availableModels.length);
                    console.log('📋 First 20 available models:', availableModels.slice(0, 20));

                    // Check our specific test models
                    const ourTestModels = [
                        'TinyLlama-1.1B-Chat-v1.0-q4f16_1-MLC',
                        'Qwen2-0.5B-Instruct-q4f16_1-MLC',
                        'Phi-3-mini-4k-instruct-q4f16_1-MLC',
                        'gemma-2-2b-it-q4f16_1-MLC'
                    ];

                    console.log('🎯 TESTING OUR MODELS:');
                    ourTestModels.forEach(modelId => {
                        const exists = availableModels.includes(modelId);
                        console.log(`${exists ? '✅' : '❌'} ${modelId} - ${exists ? 'AVAILABLE' : 'NOT FOUND'}`);
                    });

                    // Look for similar models in case exact names don't match
                    const similarModels = availableModels.filter(id =>
                        id.toLowerCase().includes('tinyllama') ||
                        id.toLowerCase().includes('phi') ||
                        id.toLowerCase().includes('gemma') ||
                        id.toLowerCase().includes('qwen')
                    );
                    console.log('🔍 Similar models found:', similarModels.slice(0, 10));

                    // Show some working model examples
                    console.log('💡 To update config, use model IDs from the lists above');
                    console.log('💡 Common working patterns: *-q4f16_1-MLC, *-q4f32_1-MLC');

                    if (!availableModels.includes(modelId)) {
                        console.warn(`⚠️ Model ${modelId} not found in prebuilt list`);
                        console.log('🔍 Trying anyway...');
                    } else {
                        console.log(`✅ Model ${modelId} found in prebuilt list`);
                    }
                } else {
                    console.warn('⚠️ WebLLM prebuilt config not available');
                }

                this.engine = await webllm.CreateMLCEngine(modelId, {
                    initProgressCallback: (progress) => {
                        console.log(`📊 AI Loading Progress:`, progress);
                        if (options.onProgress) {
                            options.onProgress(progress);
                        }
                    }
                });

            } catch (importError) {
                console.error('❌ WebLLM import failed:', importError);
                throw new Error(`Failed to import WebLLM: ${importError.message}`);
            }

            this.currentModel = modelId;
            this.isReady = true;

            console.log(`✅ AI engine initialized successfully with ${modelConfig.name}`);
            console.log(`🎯 Model loaded from Hugging Face: ${modelConfig.huggingFaceRepo}`);
            return true;

        } catch (error) {
            console.error('❌ Failed to initialize AI engine:', error);
            console.error('🔍 Error details:', error.message);
            console.error('💡 Check if model ID is correct and Hugging Face is accessible');

            // Offer fallback to demo mode
            console.log('🎭 Offering demo mode as fallback...');
            const useDemo = confirm('AI model failed to load. Would you like to use Demo Mode instead? (Click OK for Demo Mode, Cancel to retry)');

            if (useDemo) {
                console.log('🎭 Switching to demo mode...');
                this.isDemoMode = true;
                this.currentModel = 'demo-mode';
                this.isReady = true;

                if (options.onProgress) {
                    options.onProgress({ progress: 1.0, text: 'Demo mode activated!' });
                }

                return true;
            } else {
                this.isReady = false;
                throw error;
            }
        }
    }

    async generateResponse(prompt, context = {}) {
        if (!this.isReady) {
            throw new Error('AI engine not initialized');
        }

        try {
            // Handle demo mode
            if (this.isDemoMode) {
                console.log('🎭 Generating demo response...');

                // Add a small delay to simulate thinking
                await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 1200));

                // Get next demo response
                const response = DEMO_RESPONSES[this.demoResponseIndex % DEMO_RESPONSES.length];
                this.demoResponseIndex++;

                // Add some variation based on user input
                if (prompt.toLowerCase().includes('look') || prompt.toLowerCase().includes('examine')) {
                    return `You carefully observe your surroundings. ${response}`;
                } else if (prompt.toLowerCase().includes('talk') || prompt.toLowerCase().includes('speak')) {
                    return `Your words echo through the crystalline halls. ${response}`;
                } else if (prompt.toLowerCase().includes('go') || prompt.toLowerCase().includes('move')) {
                    return `You move forward with purpose. ${response}`;
                } else {
                    return response;
                }
            }

            // Regular WebLLM response
            if (!this.engine) {
                throw new Error('WebLLM engine not initialized');
            }

            const response = await this.engine.chat.completions.create({
                messages: [{ role: "user", content: prompt }],
                temperature: context.temperature || 0.8,
                max_tokens: context.maxTokens || 250
            });

            return response.choices[0].message.content;
        } catch (error) {
            console.error('❌ Failed to generate AI response:', error);
            throw error;
        }
    }
}

// ===== MULTIPLAYER MANAGER (WEBRTC P2P SYNC) =====
class MultiplayerManager {
    constructor(gameApp) {
        this.gameApp = gameApp;
        this.isHost = false;
        this.isConnected = false;
        this.playerId = this.generatePlayerId();
        this.gameRoomId = null;
        this.connectedPlayers = new Map();
        this.hostPlayerId = null;
        this.isMultiplayerMode = false;

        // WebRTC connections for real-time sync
        this.peerConnections = new Map();
        this.dataChannels = new Map();
        this.signalingChannel = null;

        // Game state synchronization
        this.lastSyncedState = null;
        this.messageQueue = [];

        // Use a simple signaling server (Firebase Realtime Database free tier)
        this.signalingUrl = 'https://agartha-signaling-default-rtdb.firebaseio.com/';

        console.log('🎮 Multiplayer Manager initialized for real-time sync');
    }

    generatePlayerId() {
        return 'player_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }

    generateRoomId() {
        return 'ROOM_' + Math.random().toString(36).substr(2, 6).toUpperCase();
    }

    async hostGame(playerData, aiModel) {
        this.gameRoomId = this.generateRoomId();
        this.isHost = true;
        this.isMultiplayerMode = true;
        this.hostPlayerId = this.playerId;

        console.log(`🎮 Hosting game room: ${this.gameRoomId}`);

        try {
            // Set up signaling for incoming connections
            await this.setupHostSignaling();

            // Initialize host's game state
            this.initializeHostGameState(playerData, aiModel);

            return {
                success: true,
                roomId: this.gameRoomId,
                shareUrl: `${window.location.origin}${window.location.pathname}?room=${this.gameRoomId}`
            };
        } catch (error) {
            console.error('Failed to set up host:', error);
            return { success: false, error: error.message };
        }
    }

    async joinGame(roomId, playerData) {
        this.gameRoomId = roomId;
        this.isHost = false;
        this.isMultiplayerMode = true;

        console.log(`🎮 Joining game room: ${roomId}`);

        try {
            // Connect to host via WebRTC
            await this.connectToHost(playerData);

            return { success: true };
        } catch (error) {
            console.error('Failed to join game:', error);
            return { success: false, error: error.message };
        }
    }

    async setupHostSignaling() {
        // Create a simple signaling mechanism using localStorage and polling
        // This works entirely client-side for GoDaddy hosting
        const roomData = {
            hostId: this.playerId,
            created: Date.now(),
            players: {},
            offers: {},
            answers: {},
            candidates: {}
        };

        // Store room data in localStorage with expiration
        localStorage.setItem(`agartha_room_${this.gameRoomId}`, JSON.stringify(roomData));

        // Start polling for connection requests
        this.startSignalingPoll();

        console.log('✅ Host signaling set up');
    }

    async connectToHost(playerData) {
        // Look for the room in localStorage
        const roomDataStr = localStorage.getItem(`agartha_room_${this.gameRoomId}`);
        if (!roomDataStr) {
            throw new Error('Room not found');
        }

        const roomData = JSON.parse(roomDataStr);
        this.hostPlayerId = roomData.hostId;

        // Create WebRTC connection to host
        await this.createPeerConnection(this.hostPlayerId, playerData);

        console.log('✅ Connected to host');
    }

    async createPeerConnection(targetId, playerData) {
        const pc = new RTCPeerConnection({
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' }
            ]
        });

        this.peerConnections.set(targetId, pc);

        // Create data channel for game communication
        if (this.isHost) {
            const channel = pc.createDataChannel('gameData', { ordered: true });
            this.setupDataChannel(channel, targetId);
        } else {
            pc.ondatachannel = (event) => {
                this.setupDataChannel(event.channel, targetId);
            };
        }

        // Handle ICE candidates
        pc.onicecandidate = (event) => {
            if (event.candidate) {
                this.sendSignalingMessage(targetId, 'candidate', event.candidate);
            }
        };

        // Create and send offer/answer
        if (!this.isHost) {
            const offer = await pc.createOffer();
            await pc.setLocalDescription(offer);
            this.sendSignalingMessage(targetId, 'offer', offer);
        }

        return pc;
    }

    setupDataChannel(channel, peerId) {
        this.dataChannels.set(peerId, channel);

        channel.onopen = () => {
            console.log(`✅ Data channel open with ${peerId}`);
            this.isConnected = true;

            // Send initial sync if host
            if (this.isHost) {
                this.syncGameStateToPlayer(peerId);
            }
        };

        channel.onmessage = (event) => {
            this.handlePeerMessage(JSON.parse(event.data), peerId);
        };

        channel.onclose = () => {
            console.log(`🔌 Data channel closed with ${peerId}`);
            this.dataChannels.delete(peerId);
            this.connectedPlayers.delete(peerId);
        };
    }

    initializeHostGameState(playerData, aiModel) {
        // Store host's game state for syncing
        this.hostGameState = {
            players: new Map([[this.playerId, playerData]]),
            messages: [],
            currentLocation: 'Crystal Gates of Shambhala',
            gameStats: {
                actionsPerformed: 0,
                startTime: Date.now()
            }
        };

        console.log('🎮 Host game state initialized');
    }

    sendPlayerAction(action, playerData) {
        if (this.isHost) {
            // Host processes actions locally and syncs to all players
            this.processHostAction(action, playerData);
            return;
        }

        // Send action to host via WebRTC
        const message = {
            type: 'PLAYER_ACTION',
            playerId: this.playerId,
            playerData: playerData,
            action: action,
            timestamp: Date.now()
        };

        this.sendToHost(message);
        console.log(`📤 Action sent to host: ${action}`);
    }

    async processHostAction(action, playerData) {
        // Host processes the action through AI
        console.log(`🤖 Host processing action: ${action}`);

        // Add player message to host's UI
        this.addMessageToUI('player', playerData.name, action);

        // Process through AI if available
        if (this.gameApp.aiManager && this.gameApp.aiManager.isReady) {
            try {
                // Generate AI response
                const response = await this.gameApp.aiManager.generateResponse(action, playerData, {});

                // Add AI response to host's UI
                this.addMessageToUI('dm', 'The Eternal Keeper', response);

                // Sync the complete interaction to all players
                this.syncInteractionToAllPlayers(playerData, action, response);

            } catch (error) {
                console.error('AI response error:', error);
                const fallbackResponse = `The mystical energies swirl around ${playerData.name} as they ${action}.`;
                this.addMessageToUI('dm', 'The Eternal Keeper', fallbackResponse);
                this.syncInteractionToAllPlayers(playerData, action, fallbackResponse);
            }
        } else {
            // Fallback response if AI not ready
            const fallbackResponse = `The ancient halls echo as ${playerData.name} ${action}.`;
            this.addMessageToUI('dm', 'The Eternal Keeper', fallbackResponse);
            this.syncInteractionToAllPlayers(playerData, action, fallbackResponse);
        }
    }

    syncInteractionToAllPlayers(playerData, action, response) {
        const syncMessage = {
            type: 'GAME_INTERACTION',
            playerData: playerData,
            action: action,
            response: response,
            timestamp: Date.now()
        };

        // Send to all connected players
        this.dataChannels.forEach((channel, peerId) => {
            if (channel.readyState === 'open') {
                channel.send(JSON.stringify(syncMessage));
            }
        });

        console.log(`📡 Synced interaction to ${this.dataChannels.size} players`);
    }

    handlePeerMessage(message, peerId) {
        console.log(`📨 Received message from ${peerId}:`, message.type);

        switch (message.type) {
            case 'PLAYER_ACTION':
                if (this.isHost) {
                    // Host processes actions from other players
                    this.processHostAction(message.action, message.playerData);
                }
                break;

            case 'GAME_INTERACTION':
                if (!this.isHost) {
                    // Players receive synced interactions from host
                    this.applySyncedInteraction(message);
                }
                break;

            case 'GAME_STATE_SYNC':
                if (!this.isHost) {
                    // Players receive full game state updates
                    this.applySyncedGameState(message.gameState);
                }
                break;

            case 'PLAYER_JOINED':
                this.handlePlayerJoined(message.playerData);
                break;
        }
    }

    applySyncedInteraction(message) {
        // Add the player action and AI response to this player's UI
        this.addMessageToUI('player', message.playerData.name, message.action);
        this.addMessageToUI('dm', 'The Eternal Keeper', message.response);

        console.log(`🔄 Applied synced interaction from ${message.playerData.name}`);
    }

    addMessageToUI(type, author, content) {
        // Add message to the game UI
        const gameOutput = document.getElementById('gameOutput');
        if (gameOutput) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type === 'player' ? 'player-message' : 'ai-message'}`;

            const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

            messageDiv.innerHTML = `
                <div class="message-content">
                    <strong>${author}:</strong> ${content}
                    <span class="message-time">${timestamp}</span>
                </div>
            `;

            gameOutput.appendChild(messageDiv);
            gameOutput.scrollTop = gameOutput.scrollHeight;

            // Remove AI thinking indicator if present
            const thinkingElement = document.getElementById('ai-thinking');
            if (thinkingElement) {
                thinkingElement.remove();
            }
        }
    }

    sendToHost(message) {
        if (this.hostPlayerId && this.dataChannels.has(this.hostPlayerId)) {
            const channel = this.dataChannels.get(this.hostPlayerId);
            if (channel.readyState === 'open') {
                channel.send(JSON.stringify(message));
            }
        }
    }

    syncGameStateToPlayer(peerId) {
        if (this.isHost && this.hostGameState) {
            const syncMessage = {
                type: 'GAME_STATE_SYNC',
                gameState: this.hostGameState,
                timestamp: Date.now()
            };

            const channel = this.dataChannels.get(peerId);
            if (channel && channel.readyState === 'open') {
                channel.send(JSON.stringify(syncMessage));
            }
        }
    }

    startSignalingPoll() {
        // Poll for connection requests every 2 seconds
        this.signalingInterval = setInterval(() => {
            this.checkForConnectionRequests();
        }, 2000);
    }

    checkForConnectionRequests() {
        const roomDataStr = localStorage.getItem(`agartha_room_${this.gameRoomId}`);
        if (!roomDataStr) return;

        const roomData = JSON.parse(roomDataStr);

        // Check for new offers
        Object.keys(roomData.offers || {}).forEach(async (playerId) => {
            if (!this.peerConnections.has(playerId)) {
                const offer = roomData.offers[playerId];
                await this.handleIncomingOffer(playerId, offer);
            }
        });
    }

    async handleIncomingOffer(playerId, offer) {
        console.log(`📞 Handling incoming offer from ${playerId}`);

        const pc = await this.createPeerConnection(playerId, null);
        await pc.setRemoteDescription(offer);

        const answer = await pc.createAnswer();
        await pc.setLocalDescription(answer);

        // Store answer in localStorage
        this.sendSignalingMessage(playerId, 'answer', answer);
    }

    sendSignalingMessage(targetId, type, data) {
        const roomDataStr = localStorage.getItem(`agartha_room_${this.gameRoomId}`);
        if (!roomDataStr) return;

        const roomData = JSON.parse(roomDataStr);

        if (type === 'offer') {
            roomData.offers = roomData.offers || {};
            roomData.offers[this.playerId] = data;
        } else if (type === 'answer') {
            roomData.answers = roomData.answers || {};
            roomData.answers[this.playerId] = data;
        } else if (type === 'candidate') {
            roomData.candidates = roomData.candidates || {};
            roomData.candidates[this.playerId] = roomData.candidates[this.playerId] || [];
            roomData.candidates[this.playerId].push(data);
        }

        localStorage.setItem(`agartha_room_${this.gameRoomId}`, JSON.stringify(roomData));

        // Start polling for responses if we're not the host
        if (!this.isHost && !this.responsePolling) {
            this.startResponsePolling();
        }
    }

    startResponsePolling() {
        this.responsePolling = setInterval(() => {
            this.checkForSignalingResponses();
        }, 1000);
    }

    async checkForSignalingResponses() {
        const roomDataStr = localStorage.getItem(`agartha_room_${this.gameRoomId}`);
        if (!roomDataStr) return;

        const roomData = JSON.parse(roomDataStr);

        // Check for answers to our offers
        if (roomData.answers && roomData.answers[this.hostPlayerId]) {
            const answer = roomData.answers[this.hostPlayerId];
            const pc = this.peerConnections.get(this.hostPlayerId);
            if (pc && pc.remoteDescription === null) {
                await pc.setRemoteDescription(answer);
            }
        }

        // Check for ICE candidates
        if (roomData.candidates && roomData.candidates[this.hostPlayerId]) {
            const candidates = roomData.candidates[this.hostPlayerId];
            const pc = this.peerConnections.get(this.hostPlayerId);
            if (pc) {
                for (const candidate of candidates) {
                    try {
                        await pc.addIceCandidate(candidate);
                    } catch (error) {
                        console.warn('Failed to add ICE candidate:', error);
                    }
                }
            }
        }
    }

    handlePlayerJoined(playerData) {
        this.connectedPlayers.set(playerData.id, playerData);
        console.log(`👤 Player joined: ${playerData.name}`);

        // Update UI to show connected players
        this.updatePlayersDisplay();
    }

    updatePlayersDisplay() {
        const playersElement = document.getElementById('multiplayerPlayersList');
        if (playersElement) {
            let playersHTML = '<h4>👥 Connected Players</h4>';

            // Add host
            if (this.isHost) {
                playersHTML += '<div class="player-item current-player">👑 You (Host)</div>';
            } else {
                playersHTML += '<div class="player-item">👑 Host</div>';
            }

            // Add other players
            this.connectedPlayers.forEach((playerData, playerId) => {
                const isCurrentPlayer = playerId === this.playerId;
                playersHTML += `<div class="player-item ${isCurrentPlayer ? 'current-player' : ''}">
                    👤 ${playerData.name} ${isCurrentPlayer ? '(You)' : ''}
                </div>`;
            });

            playersElement.innerHTML = playersHTML;
        }
    }

    getStatus() {
        return {
            isConnected: this.isConnected,
            isHost: this.isHost,
            roomId: this.gameRoomId,
            playerId: this.playerId,
            connectedPlayers: Array.from(this.connectedPlayers.values())
        };
    }

    disconnect() {
        // Close all peer connections
        this.peerConnections.forEach(pc => pc.close());
        this.peerConnections.clear();

        // Close all data channels
        this.dataChannels.clear();

        // Stop polling
        if (this.signalingInterval) {
            clearInterval(this.signalingInterval);
        }
        if (this.responsePolling) {
            clearInterval(this.responsePolling);
        }

        // Clean up room data if host
        if (this.isHost && this.gameRoomId) {
            localStorage.removeItem(`agartha_room_${this.gameRoomId}`);
        }

        this.isMultiplayerMode = false;
        this.isHost = false;
        this.isConnected = false;
        this.gameRoomId = null;
        this.connectedPlayers.clear();

        console.log('🔌 Disconnected from multiplayer session');
    }
}

// ===== MAIN APPLICATION =====
class AgarthaApp {
    constructor() {
        this.aiManager = new AIManager();
        this.multiplayerManager = new MultiplayerManager(this);
        this.selectedModel = null;
        this.gameState = {
            player: null,
            currentLocation: 'Crystal Gates of Shambhala',
            inventory: [],
            experience: 0,
            level: 1
        };
    }

    async initialize() {
        console.log('🚀 Agartha RPG initializing...');

        try {
            // Verify model access
            await this.aiManager.verifyModelAccess();

            // Populate model options
            this.populateModelOptions();

            // Set up event listeners
            this.setupEventListeners();

            // Add multiplayer button
            this.addMultiplayerButton();

            console.log('✅ Agartha RPG initialized successfully');

        } catch (error) {
            console.error('❌ Failed to initialize Agartha RPG:', error);
        }
    }

    addMultiplayerButton() {
        const buttonContainer = document.querySelector('.game-modes') || document.querySelector('.main-actions') || document.querySelector('.model-selection');
        if (buttonContainer) {
            const multiplayerBtn = document.createElement('button');
            multiplayerBtn.className = 'btn-secondary multiplayer-btn';
            multiplayerBtn.innerHTML = '🎮 Multiplayer';
            multiplayerBtn.style.cssText = `
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                color: white;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                margin: 10px 5px;
                display: inline-block;
            `;
            multiplayerBtn.onmouseover = () => {
                multiplayerBtn.style.transform = 'translateY(-2px)';
                multiplayerBtn.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';
            };
            multiplayerBtn.onmouseout = () => {
                multiplayerBtn.style.transform = 'translateY(0)';
                multiplayerBtn.style.boxShadow = 'none';
            };
            multiplayerBtn.onclick = () => this.showMultiplayerSetup();

            buttonContainer.appendChild(multiplayerBtn);
        }
    }

    showMultiplayerSetup() {
        const modalContent = `
            <div style="padding: 20px; max-width: 600px;">
                <h3 style="color: #00ffff; margin-bottom: 20px;">🎮 Multiplayer Setup</h3>
                <p style="color: #ccc; margin-bottom: 20px;">Choose how you want to play with others:</p>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                    <div style="background: rgba(255,255,255,0.05); border: 1px solid rgba(255,255,255,0.1); border-radius: 12px; padding: 20px; text-align: center;">
                        <div style="font-size: 3rem; margin-bottom: 15px;">👑</div>
                        <h4 style="color: #00ffff; margin: 0 0 10px 0;">Host Game</h4>
                        <p style="color: #ccc; font-size: 0.9rem; margin-bottom: 15px;">Run the AI on your device and let others join</p>
                        <button onclick="window.agarthaApp.showHostSetup()" style="background: #00ffff; color: black; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-weight: 600;">
                            Host Game
                        </button>
                    </div>

                    <div style="background: rgba(255,255,255,0.05); border: 1px solid rgba(255,255,255,0.1); border-radius: 12px; padding: 20px; text-align: center;">
                        <div style="font-size: 3rem; margin-bottom: 15px;">👤</div>
                        <h4 style="color: #00ffff; margin: 0 0 10px 0;">Join Game</h4>
                        <p style="color: #ccc; font-size: 0.9rem; margin-bottom: 15px;">Join someone else's hosted game</p>
                        <button onclick="window.agarthaApp.showJoinSetup()" style="background: #ffd700; color: black; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-weight: 600;">
                            Join Game
                        </button>
                    </div>
                </div>

                <div style="background: rgba(0,255,255,0.1); border: 1px solid rgba(0,255,255,0.3); border-radius: 8px; padding: 15px; margin-top: 20px;">
                    <h4 style="color: #00ffff; margin: 0 0 10px 0;">📋 How It Works</h4>
                    <p style="color: #ccc; margin: 5px 0; font-size: 0.9rem;"><strong>Host:</strong> Downloads AI model, runs game, shares room code</p>
                    <p style="color: #ccc; margin: 5px 0; font-size: 0.9rem;"><strong>Players:</strong> Use room code to join, no downloads needed</p>
                    <p style="color: #ccc; margin: 5px 0; font-size: 0.9rem;"><strong>Note:</strong> This is a simplified multiplayer demo for GoDaddy hosting</p>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button onclick="document.getElementById('modalOverlay').style.display='none'" style="background: #666; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">
                        Close
                    </button>
                </div>
            </div>
        `;

        this.showModal('Multiplayer Setup', modalContent);
    }

    showHostSetup() {
        const modalContent = `
            <div style="padding: 20px; max-width: 500px;">
                <h3 style="color: #00ffff; margin-bottom: 20px;">👑 Host Multiplayer Game</h3>

                <div style="margin: 20px 0;">
                    <label style="display: block; color: #ffd700; font-weight: 600; margin-bottom: 5px;">Your Character Name:</label>
                    <input type="text" id="hostPlayerName" placeholder="Enter your character name" style="width: 100%; padding: 12px; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; color: white;">
                </div>

                <div style="margin: 20px 0;">
                    <label style="display: block; color: #ffd700; font-weight: 600; margin-bottom: 5px;">Character Class:</label>
                    <select id="hostPlayerClass" style="width: 100%; padding: 12px; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; color: white;">
                        <option value="mystic">🔮 Mystic - Master of ancient wisdom</option>
                        <option value="explorer">🗺️ Explorer - Brave adventurer</option>
                        <option value="scholar">📚 Scholar - Keeper of knowledge</option>
                        <option value="guardian">⚔️ Guardian - Protector of secrets</option>
                    </select>
                </div>

                <div style="display: flex; gap: 15px; margin-top: 30px;">
                    <button onclick="window.agarthaApp.showMultiplayerSetup()" style="flex: 1; background: #666; color: white; border: none; padding: 12px; border-radius: 8px; cursor: pointer;">
                        ← Back
                    </button>
                    <button onclick="window.agarthaApp.startHosting()" style="flex: 1; background: #00ffff; color: black; border: none; padding: 12px; border-radius: 8px; cursor: pointer; font-weight: 600;">
                        🚀 Start Hosting
                    </button>
                </div>
            </div>
        `;
        this.showModal('Host Game', modalContent);
    }

    showJoinSetup() {
        // Check for room code in URL
        const urlParams = new URLSearchParams(window.location.search);
        const roomCode = urlParams.get('room') || '';

        const modalContent = `
            <div style="padding: 20px; max-width: 500px;">
                <h3 style="color: #00ffff; margin-bottom: 20px;">👤 Join Multiplayer Game</h3>

                <div style="margin: 20px 0;">
                    <label style="display: block; color: #ffd700; font-weight: 600; margin-bottom: 5px;">Room Code:</label>
                    <input type="text" id="roomCode" value="${roomCode}" placeholder="Enter room code (e.g., ROOM_ABC123)" style="width: 100%; padding: 12px; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; color: white; text-transform: uppercase;">
                    <small style="color: #999; font-size: 0.8rem;">Get this code from the game host</small>
                </div>

                <div style="margin: 20px 0;">
                    <label style="display: block; color: #ffd700; font-weight: 600; margin-bottom: 5px;">Your Character Name:</label>
                    <input type="text" id="joinPlayerName" placeholder="Enter your character name" style="width: 100%; padding: 12px; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; color: white;">
                </div>

                <div style="margin: 20px 0;">
                    <label style="display: block; color: #ffd700; font-weight: 600; margin-bottom: 5px;">Character Class:</label>
                    <select id="joinPlayerClass" style="width: 100%; padding: 12px; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; color: white;">
                        <option value="mystic">🔮 Mystic - Master of ancient wisdom</option>
                        <option value="explorer">🗺️ Explorer - Brave adventurer</option>
                        <option value="scholar">📚 Scholar - Keeper of knowledge</option>
                        <option value="guardian">⚔️ Guardian - Protector of secrets</option>
                    </select>
                </div>

                <div style="display: flex; gap: 15px; margin-top: 30px;">
                    <button onclick="window.agarthaApp.showMultiplayerSetup()" style="flex: 1; background: #666; color: white; border: none; padding: 12px; border-radius: 8px; cursor: pointer;">
                        ← Back
                    </button>
                    <button onclick="window.agarthaApp.joinGame()" style="flex: 1; background: #ffd700; color: black; border: none; padding: 12px; border-radius: 8px; cursor: pointer; font-weight: 600;">
                        🎮 Join Game
                    </button>
                </div>
            </div>
        `;
        this.showModal('Join Game', modalContent);
    }

    async startHosting() {
        const playerName = document.getElementById('hostPlayerName')?.value;
        const playerClass = document.getElementById('hostPlayerClass')?.value;

        if (!playerName || !playerClass) {
            alert('Please fill in all fields');
            return;
        }

        const playerData = {
            name: playerName,
            class: playerClass,
            id: this.multiplayerManager.generatePlayerId()
        };

        const result = await this.multiplayerManager.hostGame(playerData, this.selectedModel);

        if (result.success) {
            // Set up the game state for hosting
            this.gameState.player = playerData;

            document.getElementById('modalOverlay').style.display = 'none';
            this.showHostSuccess(result);
        } else {
            alert(`Failed to host game: ${result.error}`);
        }
    }

    async joinGame() {
        const roomCode = document.getElementById('roomCode')?.value.toUpperCase();
        const playerName = document.getElementById('joinPlayerName')?.value;
        const playerClass = document.getElementById('joinPlayerClass')?.value;

        if (!roomCode || !playerName || !playerClass) {
            alert('Please fill in all fields');
            return;
        }

        const playerData = {
            name: playerName,
            class: playerClass,
            id: this.multiplayerManager.generatePlayerId()
        };

        const result = await this.multiplayerManager.joinGame(roomCode, playerData);

        if (result.success) {
            // Set up the game state for joining
            this.gameState.player = playerData;
            this.selectedModel = 'demo-mode'; // Players don't need to download AI

            document.getElementById('modalOverlay').style.display = 'none';

            // Show connection status
            alert(`Successfully joined room ${roomCode}! Connecting to host...`);

            // Update UI and start game
            this.updateMultiplayerStatus();
            this.addMultiplayerGameUI();

            // Switch to game view
            this.switchToGameView();
        } else {
            alert(`Failed to join game: ${result.error}`);
        }
    }

    showHostSuccess(result) {
        const modalContent = `
            <div style="padding: 20px; text-align: center;">
                <h3 style="color: #00ffff; margin-bottom: 20px;">🎉 Game Hosted Successfully!</h3>

                <div style="background: rgba(0,255,255,0.1); border: 1px solid rgba(0,255,255,0.3); border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h4 style="color: #ffd700; margin: 0 0 15px 0;">📋 Room Information</h4>
                    <div style="margin-bottom: 15px;">
                        <label style="color: #ffd700; font-weight: 600;">Room Code:</label>
                        <div style="background: rgba(255,255,255,0.1); padding: 8px 12px; border-radius: 6px; font-family: monospace; color: #00ffff; margin: 5px 0; font-size: 1.2rem; font-weight: bold;">
                            ${result.roomId}
                        </div>
                        <button onclick="navigator.clipboard.writeText('${result.roomId}')" style="background: #ffd700; color: black; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">
                            📋 Copy Code
                        </button>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="color: #ffd700; font-weight: 600;">Share URL:</label>
                        <div style="background: rgba(255,255,255,0.1); padding: 8px 12px; border-radius: 6px; font-family: monospace; color: #00ffff; margin: 5px 0; font-size: 0.9rem; word-break: break-all;">
                            ${result.shareUrl}
                        </div>
                        <button onclick="navigator.clipboard.writeText('${result.shareUrl}')" style="background: #ffd700; color: black; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">
                            📋 Copy URL
                        </button>
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <h4 style="color: #ffd700;">📢 Next Steps</h4>
                    <ol style="text-align: left; color: #ccc; padding-left: 20px;">
                        <li>Share the room code or URL with your friends</li>
                        <li>Wait for players to join (this is a demo version)</li>
                        <li>Start your adventure together!</li>
                    </ol>
                </div>

                <button onclick="window.agarthaApp.continueToGame()" style="background: #00ffff; color: black; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 1rem;">
                    🚀 Continue to Game
                </button>
            </div>
        `;
        this.showModal('Game Hosted!', modalContent);
    }

    continueToGame() {
        document.getElementById('modalOverlay').style.display = 'none';

        // Initialize AI for host if needed
        if (this.multiplayerManager.isHost && this.selectedModel && !this.aiManager.isReady) {
            this.initializeAIForHost();
        }

        // Switch to game view
        this.switchToGameView();

        // Update multiplayer status display
        this.updateMultiplayerStatus();

        // Add multiplayer UI elements to game
        this.addMultiplayerGameUI();
    }

    async initializeAIForHost() {
        try {
            console.log('🤖 Initializing AI for host...');
            await this.aiManager.initializeEngine(this.selectedModel);
            console.log('✅ Host AI ready for multiplayer');
        } catch (error) {
            console.error('❌ Failed to initialize host AI:', error);
            alert('Warning: AI initialization failed. You can still host, but responses will be limited.');
        }
    }

    updateMultiplayerStatus() {
        const statusElement = document.getElementById('multiplayerStatus');
        if (statusElement && this.multiplayerManager.isMultiplayerMode) {
            const status = this.multiplayerManager.getStatus();
            statusElement.innerHTML = `
                <div class="multiplayer-active">
                    🎮 ${status.isHost ? 'Host' : 'Player'}: ${status.roomId}
                    <span class="connection-status">${status.isConnected ? '🟢' : '🟡'}</span>
                </div>
            `;
        }
    }

    addMultiplayerGameUI() {
        if (!this.multiplayerManager.isMultiplayerMode) return;

        // Add players list to sidebar if it exists
        const sidebar = document.querySelector('.game-sidebar') || document.querySelector('.inventory');
        if (sidebar) {
            const multiplayerPanel = document.createElement('div');
            multiplayerPanel.className = 'multiplayer-panel';
            multiplayerPanel.style.cssText = `
                background: rgba(255,255,255,0.05);
                border-radius: 10px;
                padding: 15px;
                margin-top: 20px;
            `;

            multiplayerPanel.innerHTML = `
                <div id="multiplayerRoomInfo" style="margin-bottom: 15px;">
                    <h4 style="color: #00ffff; margin: 0 0 10px 0; font-size: 1rem;">🎮 Room: ${this.multiplayerManager.gameRoomId}</h4>
                    <p style="color: #ccc; margin: 5px 0; font-size: 0.85rem;">Role: ${this.multiplayerManager.isHost ? '👑 Host (AI Runner)' : '👤 Player'}</p>
                </div>
                <div id="multiplayerPlayersList">
                    <!-- Players list will be populated -->
                </div>
            `;

            sidebar.appendChild(multiplayerPanel);

            // Update players display
            this.multiplayerManager.updatePlayersDisplay();
        }
    }

    switchToGameView() {
        // Hide setup screens and show game interface
        const setupScreens = document.querySelectorAll('.screen');
        setupScreens.forEach(screen => {
            if (screen.id !== 'gameContainer') {
                screen.style.display = 'none';
            }
        });

        const gameScreen = document.getElementById('gameContainer');
        if (gameScreen) {
            gameScreen.style.display = 'flex';
        }

        // Enable game input
        const gameInput = document.getElementById('gameInput');
        const sendBtn = document.getElementById('sendBtn');
        if (gameInput) gameInput.disabled = false;
        if (sendBtn) sendBtn.disabled = false;

        // Add welcome message for multiplayer
        if (this.multiplayerManager.isMultiplayerMode) {
            const gameOutput = document.getElementById('gameOutput');
            if (gameOutput) {
                const welcomeMsg = document.createElement('div');
                welcomeMsg.className = 'message system-message';
                welcomeMsg.innerHTML = `
                    <div class="message-content">
                        🎮 <strong>Multiplayer ${this.multiplayerManager.isHost ? 'Host' : 'Player'} Mode</strong><br>
                        ${this.multiplayerManager.isHost ?
                            'You are hosting this game. Your AI responses will be shared with all players.' :
                            'You have joined a multiplayer game. Actions will be processed by the host.'
                        }
                    </div>
                `;
                gameOutput.appendChild(welcomeMsg);
                gameOutput.scrollTop = gameOutput.scrollHeight;
            }
        }
    }

    showModal(title, content) {
        // Create modal if it doesn't exist
        let modalOverlay = document.getElementById('modalOverlay');
        if (!modalOverlay) {
            modalOverlay = document.createElement('div');
            modalOverlay.id = 'modalOverlay';
            modalOverlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
            `;

            const modal = document.createElement('div');
            modal.id = 'modal';
            modal.style.cssText = `
                background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                max-width: 90vw;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
                position: relative;
            `;

            modalOverlay.appendChild(modal);
            document.body.appendChild(modalOverlay);

            // Close on overlay click
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) {
                    modalOverlay.style.display = 'none';
                }
            });
        }

        const modal = document.getElementById('modal');
        modal.innerHTML = `
            <div style="position: relative;">
                <button onclick="document.getElementById('modalOverlay').style.display='none'" style="position: absolute; top: 10px; right: 10px; background: rgba(255,255,255,0.2); border: none; color: white; width: 30px; height: 30px; border-radius: 50%; cursor: pointer; font-size: 18px; display: flex; align-items: center; justify-content: center;">×</button>
                ${content}
            </div>
        `;

        modalOverlay.style.display = 'flex';
    }

    populateModelOptions() {
        console.log('🎯 Populating model options...');
        
        const container = document.getElementById('modelOptions');
        if (!container) {
            console.error('❌ modelOptions container not found');
            return;
        }

        container.innerHTML = '';

        Object.entries(CONFIG.models).forEach(([modelId, config]) => {
            const option = document.createElement('div');
            option.className = `model-option hover-lift ${config.isDemo ? 'demo-option' : ''}`;
            option.dataset.model = modelId;

            const demoStyle = config.isDemo ? 'style="border: 2px solid #ffd700; background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05));"' : '';

            option.innerHTML = `
                <div class="model-header" ${demoStyle}>
                    <h3>${config.displayName}</h3>
                    <div class="model-specs">
                        <span class="badge ${config.isDemo ? 'badge-warning' : 'badge-primary'}">${config.size}</span>
                        <span class="badge badge-secondary">${config.speed}</span>
                        ${config.isDemo ? '<span class="badge badge-success">No Download</span>' : ''}
                    </div>
                </div>
                <p class="model-description">${config.description}</p>
                <div class="model-stats">
                    <div class="stat">
                        <span class="stat-label">Parameters:</span>
                        <span class="stat-value">${config.params}</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Context:</span>
                        <span class="stat-value">${config.contextLength}</span>
                    </div>
                    ${config.isDemo ? '<div class="stat"><span class="stat-label">Perfect for:</span><span class="stat-value">Testing & Demos</span></div>' : ''}
                </div>
            `;

            option.addEventListener('click', () => this.selectModel(modelId, option));
            container.appendChild(option);
        });
        
        console.log('✅ Model options populated');
    }

    selectModel(modelId, optionElement) {
        // Remove previous selections
        document.querySelectorAll('.model-option').forEach(opt => 
            opt.classList.remove('selected'));
        
        // Select this option
        optionElement.classList.add('selected');
        this.selectedModel = modelId;
        
        // Enable start button
        const startBtn = document.getElementById('startBtn');
        if (startBtn) {
            startBtn.disabled = false;
        }
        
        console.log(`🎯 Model selected: ${modelId}`);
    }

    setupEventListeners() {
        const startBtn = document.getElementById('startBtn');
        if (startBtn) {
            startBtn.addEventListener('click', () => this.initializeAI());
        }

        const createCharacterBtn = document.getElementById('createCharacterBtn');
        if (createCharacterBtn) {
            createCharacterBtn.addEventListener('click', () => this.createCharacter());
        }
    }

    async initializeAI() {
        if (!this.selectedModel) {
            alert('Please select an AI model first');
            return;
        }

        const startBtn = document.getElementById('startBtn');
        const statusText = document.getElementById('statusText');
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');

        try {
            startBtn.disabled = true;
            startBtn.innerHTML = '<span>🔄 Initializing AI...</span>';
            statusText.textContent = 'Downloading AI model from Hugging Face...';
            progressBar.style.display = 'block';

            await this.aiManager.initialize(this.selectedModel, {
                onProgress: (progress) => {
                    if (progress.progress !== undefined) {
                        const percent = Math.round(progress.progress * 100);
                        progressFill.style.width = `${percent}%`;
                        progressFill.textContent = `${percent}%`;
                        statusText.textContent = progress.text || `Loading... ${percent}%`;
                    }
                }
            });

            // Hide model setup and show character creation
            document.getElementById('modelSetup').classList.add('hidden');
            const charCreation = document.getElementById('characterCreation');
            charCreation.classList.remove('hidden');
            charCreation.classList.add('active');
            this.populateCharacterClasses();

        } catch (error) {
            console.error('❌ AI initialization failed:', error);
            startBtn.disabled = false;
            startBtn.innerHTML = '<span>🧙‍♂️ Initialize AI Dungeon Master</span>';
            statusText.textContent = 'Failed to initialize AI. Please try again.';
            progressBar.style.display = 'none';
            alert('Failed to initialize AI model. Please check your internet connection and try again.');
        }
    }

    populateCharacterClasses() {
        console.log('🎭 Populating character classes...');
        const container = document.getElementById('characterClasses');
        if (!container) {
            console.error('❌ characterClasses container not found');
            return;
        }
        console.log('✅ characterClasses container found');

        container.innerHTML = '';
        console.log('📋 Available character classes:', Object.keys(CONFIG.characterClasses));

        Object.entries(CONFIG.characterClasses).forEach(([className, classData]) => {
            const classOption = document.createElement('div');
            classOption.className = 'character-class hover-lift';
            classOption.dataset.class = className;
            classOption.innerHTML = `
                <h3>${classData.name}</h3>
                <h4>${classData.title}</h4>
                <p>${classData.description}</p>
                <div class="class-stats">
                    <div class="stat-grid">
                        ${Object.entries(classData.baseStats).map(([stat, value]) => 
                            `<div class="stat"><span>${stat}:</span><span>${value}</span></div>`
                        ).join('')}
                    </div>
                </div>
            `;
            
            classOption.addEventListener('click', () => {
                document.querySelectorAll('.character-class').forEach(opt => 
                    opt.classList.remove('selected'));
                classOption.classList.add('selected');
                
                const createBtn = document.getElementById('createCharacterBtn');
                if (createBtn) createBtn.disabled = false;
            });
            
            container.appendChild(classOption);
        });

        console.log('✅ Character classes populated successfully');
    }

    createCharacter() {
        const selectedClass = document.querySelector('.character-class.selected');
        if (!selectedClass) {
            alert('Please select a character class');
            return;
        }

        const playerName = document.getElementById('playerName').value.trim();
        if (!playerName) {
            alert('Please enter a character name');
            return;
        }

        const className = selectedClass.dataset.class;
        const classData = CONFIG.characterClasses[className];

        this.gameState.player = {
            name: playerName,
            class: className,
            classData: classData,
            stats: { ...classData.baseStats },
            inventory: [...classData.startingItems],
            abilities: [...classData.abilities],
            level: 1,
            experience: 0
        };

        console.log('✅ Character created:', this.gameState.player);

        // Save player data and AI model to localStorage
        localStorage.setItem('agarthaPlayer', JSON.stringify(this.gameState.player));
        localStorage.setItem('agarthaAIModel', this.selectedModel);

        // Redirect to game page
        console.log('🚀 Redirecting to game page...');
        window.location.href = 'game.html';
    }

    startGame() {
        console.log('🎮 Starting Agartha adventure...');

        // Initialize game UI
        this.updatePlayerInfo();
        this.updateInventory();
        this.showWelcomeMessage();
    }

    updatePlayerInfo() {
        const playerInfo = document.getElementById('playerInfo');
        if (playerInfo && this.gameState.player) {
            playerInfo.innerHTML = `
                <div class="player-card">
                    <h3>${this.gameState.player.name}</h3>
                    <p class="player-class">${this.gameState.player.classData.title}</p>
                    <div class="player-stats">
                        <div class="stat">Level: ${this.gameState.player.level}</div>
                        <div class="stat">XP: ${this.gameState.player.experience}</div>
                    </div>
                </div>
            `;
        }
    }

    updateInventory() {
        const inventoryList = document.getElementById('inventoryList');
        if (inventoryList && this.gameState.player) {
            inventoryList.innerHTML = this.gameState.player.inventory
                .map(item => `<div class="inventory-item">${item}</div>`)
                .join('');
        }
    }

    showWelcomeMessage() {
        const gameOutput = document.getElementById('gameOutput');
        if (gameOutput) {
            const demoNotice = this.aiManager.isDemoMode ?
                '<div class="badge badge-warning mb-md">🎭 Demo Mode Active - Using pre-written responses</div>' : '';

            const welcomeMessage = `
                <div class="message ai-message">
                    <div class="message-content">
                        ${demoNotice}
                        <h3>🌟 Welcome to Agartha, ${this.gameState.player.name}!</h3>
                        <p>As a ${this.gameState.player.classData.name}, you stand before the magnificent Crystal Gates of Shambhala. The ancient portals shimmer with otherworldly energy, and you can feel the power of the inner realm calling to you.</p>
                        <p>${this.gameState.player.classData.lore}</p>
                        <p>Your journey into the mystical underground realm begins now. What would you like to do?</p>
                        ${this.aiManager.isDemoMode ? '<p><em>Try commands like: "look around", "talk to the guardian", "examine the gates", or "go to the plaza"</em></p>' : ''}
                    </div>
                </div>
            `;
            gameOutput.innerHTML = welcomeMessage;
        }

        // Enable game input
        const gameInput = document.getElementById('gameInput');
        const sendBtn = document.getElementById('sendBtn');
        if (gameInput && sendBtn) {
            gameInput.disabled = false;
            sendBtn.disabled = false;
            gameInput.focus();

            gameInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });

            sendBtn.addEventListener('click', () => this.sendMessage());
        }
    }

    async sendMessage() {
        const gameInput = document.getElementById('gameInput');
        const gameOutput = document.getElementById('gameOutput');

        if (!gameInput || !gameOutput) return;

        const message = gameInput.value.trim();
        if (!message) return;

        // Check if we're in multiplayer mode
        if (this.multiplayerManager.isMultiplayerMode) {
            gameInput.value = '';

            // Send to multiplayer manager (it will handle UI updates)
            this.multiplayerManager.sendPlayerAction(message, this.gameState.player);

            // Show waiting indicator for non-host players
            if (!this.multiplayerManager.isHost) {
                const thinkingSpinner = `
                    <div class="message ai-message" id="ai-thinking">
                        <div class="ai-thinking-spinner">
                            <span class="typing-dots"></span>
                        </div>
                    </div>
                `;
                gameOutput.innerHTML += thinkingSpinner;
                gameOutput.scrollTop = gameOutput.scrollHeight;
            }

            return;
        }

        // Single player mode - original logic
        // Add player message to output
        const playerMessage = `
            <div class="message player-message">
                <div class="message-content">${message}</div>
            </div>
        `;
        gameOutput.innerHTML += playerMessage;
        gameInput.value = '';

        // Show AI thinking spinner
        const thinkingSpinner = `
            <div class="message ai-message" id="ai-thinking">
                <div class="ai-thinking-spinner">
                    <span class="typing-dots"></span>
                </div>
            </div>
        `;
        gameOutput.innerHTML += thinkingSpinner;

        // Scroll to bottom
        gameOutput.scrollTop = gameOutput.scrollHeight;

        try {
            // Generate AI response
            const context = {
                player: this.gameState.player,
                location: this.gameState.currentLocation,
                inventory: this.gameState.player.inventory
            };

            const prompt = `You are the AI Dungeon Master for Agartha RPG. The player is ${this.gameState.player.name}, a ${this.gameState.player.class} currently at ${this.gameState.currentLocation}.

Player action: "${message}"

Respond as the dungeon master, describing what happens next in this mystical underground realm. Keep responses engaging, immersive, and around 2-3 paragraphs.`;

            const aiResponse = await this.aiManager.generateResponse(prompt, context);

            // Remove thinking spinner
            const thinkingElement = document.getElementById('ai-thinking');
            if (thinkingElement) {
                thinkingElement.remove();
            }

            // Add AI response to output
            const aiMessage = `
                <div class="message ai-message">
                    <div class="message-content">${aiResponse}</div>
                </div>
            `;
            gameOutput.innerHTML += aiMessage;

        } catch (error) {
            console.error('❌ Failed to generate AI response:', error);

            // Remove thinking spinner
            const thinkingElement = document.getElementById('ai-thinking');
            if (thinkingElement) {
                thinkingElement.remove();
            }

            const errorMessage = `
                <div class="message ai-message error">
                    <div class="message-content">
                        <em>The mystical energies seem disrupted... Please try again.</em>
                    </div>
                </div>
            `;
            gameOutput.innerHTML += errorMessage;
        }

        // Scroll to bottom
        gameOutput.scrollTop = gameOutput.scrollHeight;
    }

    // Initialize game page with stored player data
    initializeGamePage(playerData, aiModel) {
        console.log('🎮 Initializing game page with player:', playerData);

        // Restore game state
        this.gameState.player = playerData;
        this.selectedModel = aiModel;

        // Initialize AI if not already done
        if (!this.aiManager.isReady) {
            this.aiManager.initialize(aiModel).then(() => {
                this.startGamePage();
            }).catch(error => {
                console.error('❌ Failed to initialize AI on game page:', error);
                // Continue with demo mode or show error
                this.startGamePage();
            });
        } else {
            this.startGamePage();
        }
    }

    startGamePage() {
        console.log('🌟 Starting game page...');

        // Update player info in sidebar
        this.updateGamePagePlayerInfo();
        this.updateGamePageInventory();
        this.showGamePageWelcome();

        // Enable input
        const gameInput = document.getElementById('gameInput');
        const sendBtn = document.getElementById('sendBtn');

        if (gameInput && sendBtn) {
            gameInput.disabled = false;
            sendBtn.disabled = false;
            gameInput.focus();

            // Add event listeners
            gameInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendGameMessage();
                }
            });

            sendBtn.addEventListener('click', () => this.sendGameMessage());
        }
    }

    updateGamePagePlayerInfo() {
        const playerName = document.getElementById('playerName');
        const playerClass = document.getElementById('playerClass');
        const playerStats = document.getElementById('playerStats');

        if (playerName && this.gameState.player) {
            playerName.textContent = this.gameState.player.name;
        }

        if (playerClass && this.gameState.player) {
            playerClass.textContent = this.gameState.player.classData.title;
        }

        if (playerStats && this.gameState.player) {
            playerStats.innerHTML = Object.entries(this.gameState.player.stats)
                .map(([stat, value]) => `
                    <div class="stat">
                        <span>${stat}:</span>
                        <span>${value}</span>
                    </div>
                `).join('');
        }
    }

    updateGamePageInventory() {
        const inventoryList = document.getElementById('inventoryList');
        if (inventoryList && this.gameState.player) {
            inventoryList.innerHTML = this.gameState.player.inventory
                .map(item => `<div class="inventory-item">${item}</div>`)
                .join('');
        }
    }

    showGamePageWelcome() {
        const gameOutput = document.getElementById('gameOutput');
        if (gameOutput && this.gameState.player) {
            const demoNotice = this.aiManager.isDemoMode ?
                '<div class="badge badge-warning" style="margin-bottom: 10px;">🎭 Demo Mode Active</div>' : '';

            const welcomeMessage = `
                <div class="message ai">
                    ${demoNotice}
                    <h3>🌟 Welcome to Agartha, ${this.gameState.player.name}!</h3>
                    <p>As a ${this.gameState.player.classData.name}, you stand before the magnificent Crystal Gates of Shambhala. The ancient portals shimmer with otherworldly energy, and you can feel the power of the inner realm calling to you.</p>
                    <p>${this.gameState.player.classData.lore}</p>
                    <p>Your journey into the mystical underground realm begins now. What would you like to do?</p>
                    ${this.aiManager.isDemoMode ? '<p><em>Try: "look around", "examine the gates", "talk to the guardian"</em></p>' : ''}
                </div>
            `;
            gameOutput.innerHTML = welcomeMessage;
        }
    }

    sendGameMessage() {
        const gameInput = document.getElementById('gameInput');
        const gameOutput = document.getElementById('gameOutput');

        if (!gameInput || !gameOutput) return;

        const message = gameInput.value.trim();
        if (!message) return;

        // Add player message
        const playerMessage = document.createElement('div');
        playerMessage.className = 'message player';
        playerMessage.textContent = message;
        gameOutput.appendChild(playerMessage);

        gameInput.value = '';
        gameOutput.scrollTop = gameOutput.scrollHeight;

        // Generate AI response (reuse existing method)
        this.generateAIResponse(message, gameOutput);
    }

    async generateAIResponse(message, gameOutput) {
        // Show AI thinking spinner
        const thinkingElement = document.createElement('div');
        thinkingElement.className = 'message ai';
        thinkingElement.id = 'ai-thinking-alt';
        thinkingElement.innerHTML = `
            <div class="ai-thinking-spinner">
                <span class="typing-dots"></span>
            </div>
        `;
        gameOutput.appendChild(thinkingElement);
        gameOutput.scrollTop = gameOutput.scrollHeight;

        try {
            const context = {
                player: this.gameState.player,
                location: this.gameState.currentLocation,
                inventory: this.gameState.player.inventory
            };

            const prompt = `You are the AI Dungeon Master for Agartha RPG. The player is ${this.gameState.player.name}, a ${this.gameState.player.class} currently at ${this.gameState.currentLocation}.

Player action: "${message}"

Respond as the dungeon master, describing what happens next in this mystical underground realm. Keep responses engaging, immersive, and around 2-3 paragraphs.`;

            const aiResponse = await this.aiManager.generateResponse(prompt, context);

            // Remove thinking spinner
            const spinner = document.getElementById('ai-thinking-alt');
            if (spinner) {
                spinner.remove();
            }

            // Add AI response
            const aiMessage = document.createElement('div');
            aiMessage.className = 'message ai';
            aiMessage.innerHTML = aiResponse;
            gameOutput.appendChild(aiMessage);

        } catch (error) {
            console.error('❌ Failed to generate AI response:', error);

            // Remove thinking spinner
            const spinner = document.getElementById('ai-thinking-alt');
            if (spinner) {
                spinner.remove();
            }

            const errorMessage = document.createElement('div');
            errorMessage.className = 'message ai';
            errorMessage.innerHTML = '<em>The mystical energies seem disrupted... Please try again.</em>';
            gameOutput.appendChild(errorMessage);
        }

        gameOutput.scrollTop = gameOutput.scrollHeight;
    }
}

// ===== INITIALIZE APPLICATION =====
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🌟 Starting Agartha RPG...');
    window.agarthaApp = new AgarthaApp();
    await window.agarthaApp.initialize();
});
