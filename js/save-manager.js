/**
 * AGARTHA RPG - SAVE MANAGER
 * Handles game save/load functionality with multiple save slots
 */

import { CONFIG } from './config.js';

/**
 * Save Manager Class
 * Manages game save/load operations with compression and validation
 */
export class SaveManager {
    constructor() {
        this.storageKey = CONFIG.storage.localStorageKey;
        this.maxSaveSlots = CONFIG.storage.maxSaveSlots;
        this.compressionEnabled = CONFIG.storage.compressionEnabled;
        this.encryptionEnabled = CONFIG.storage.encryptionEnabled;
        
        this.saveSlots = new Map();
        this.autoSaveSlot = 'autosave';
        
        this.initializeSaveSlots();
    }

    /**
     * Initialize save slots from localStorage
     */
    initializeSaveSlots() {
        try {
            const savedData = localStorage.getItem(this.storageKey);
            if (savedData) {
                const parsed = JSON.parse(savedData);
                
                // Load existing save slots
                if (parsed.saveSlots) {
                    for (const [slotId, saveData] of Object.entries(parsed.saveSlots)) {
                        this.saveSlots.set(slotId, saveData);
                    }
                }
                
                console.log(`💾 Loaded ${this.saveSlots.size} save slots`);
            }
        } catch (error) {
            console.warn('Failed to load save slots:', error);
            this.saveSlots.clear();
        }
    }

    /**
     * Save game to a specific slot
     */
    async saveGame(gameData, slotId = this.autoSaveSlot, metadata = {}) {
        try {
            const saveData = {
                id: slotId,
                timestamp: Date.now(),
                version: CONFIG.game.version,
                metadata: {
                    playerName: gameData.gameState?.players?.values()?.next()?.value?.name || 'Unknown',
                    location: gameData.gameState?.location || 'Unknown',
                    level: gameData.gameState?.players?.values()?.next()?.value?.level || 1,
                    playTime: gameData.gameState?.gameStats?.playTime || 0,
                    ...metadata
                },
                gameData: gameData
            };
            
            // Compress data if enabled
            if (this.compressionEnabled) {
                saveData.gameData = await this.compressData(saveData.gameData);
                saveData.compressed = true;
            }
            
            // Encrypt data if enabled
            if (this.encryptionEnabled) {
                saveData.gameData = await this.encryptData(saveData.gameData);
                saveData.encrypted = true;
            }
            
            // Validate save data
            if (!this.validateSaveData(saveData)) {
                throw new Error('Save data validation failed');
            }
            
            // Store in memory
            this.saveSlots.set(slotId, saveData);
            
            // Persist to localStorage
            await this.persistSaveSlots();
            
            console.log(`💾 Game saved to slot: ${slotId}`);
            return true;
            
        } catch (error) {
            console.error('Failed to save game:', error);
            throw error;
        }
    }

    /**
     * Load game from a specific slot
     */
    async loadGame(slotId = this.autoSaveSlot) {
        try {
            const saveData = this.saveSlots.get(slotId);
            if (!saveData) {
                return null;
            }
            
            // Validate save data
            if (!this.validateSaveData(saveData)) {
                throw new Error('Save data validation failed');
            }
            
            let gameData = saveData.gameData;
            
            // Decrypt data if needed
            if (saveData.encrypted) {
                gameData = await this.decryptData(gameData);
            }
            
            // Decompress data if needed
            if (saveData.compressed) {
                gameData = await this.decompressData(gameData);
            }
            
            console.log(`💾 Game loaded from slot: ${slotId}`);
            return {
                ...saveData,
                gameData: gameData
            };
            
        } catch (error) {
            console.error('Failed to load game:', error);
            throw error;
        }
    }

    /**
     * Get all save slots with metadata
     */
    getSaveSlots() {
        const slots = [];
        
        for (const [slotId, saveData] of this.saveSlots.entries()) {
            slots.push({
                id: slotId,
                timestamp: saveData.timestamp,
                metadata: saveData.metadata,
                isAutoSave: slotId === this.autoSaveSlot
            });
        }
        
        // Sort by timestamp (newest first)
        return slots.sort((a, b) => b.timestamp - a.timestamp);
    }

    /**
     * Delete a save slot
     */
    async deleteSave(slotId) {
        if (slotId === this.autoSaveSlot) {
            throw new Error('Cannot delete autosave slot');
        }
        
        if (this.saveSlots.has(slotId)) {
            this.saveSlots.delete(slotId);
            await this.persistSaveSlots();
            console.log(`💾 Deleted save slot: ${slotId}`);
            return true;
        }
        
        return false;
    }

    /**
     * Create a new manual save slot
     */
    async createManualSave(gameData, saveName) {
        const slotId = `manual_${Date.now()}`;
        const metadata = {
            name: saveName,
            isManual: true
        };
        
        return await this.saveGame(gameData, slotId, metadata);
    }

    /**
     * Export save data as downloadable file
     */
    exportSave(slotId) {
        const saveData = this.saveSlots.get(slotId);
        if (!saveData) {
            throw new Error('Save slot not found');
        }
        
        const exportData = {
            agarthaRPGSave: true,
            version: CONFIG.game.version,
            exportTimestamp: Date.now(),
            saveData: saveData
        };
        
        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `agartha_save_${saveData.metadata.playerName}_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(link.href);
    }

    /**
     * Import save data from file
     */
    async importSave(fileContent) {
        try {
            const importData = JSON.parse(fileContent);
            
            // Validate import data
            if (!importData.agarthaRPGSave || !importData.saveData) {
                throw new Error('Invalid save file format');
            }
            
            // Check version compatibility
            if (importData.version !== CONFIG.game.version) {
                console.warn(`Save file version mismatch: ${importData.version} vs ${CONFIG.game.version}`);
            }
            
            const slotId = `imported_${Date.now()}`;
            const saveData = importData.saveData;
            saveData.metadata.imported = true;
            
            this.saveSlots.set(slotId, saveData);
            await this.persistSaveSlots();
            
            console.log(`💾 Imported save to slot: ${slotId}`);
            return slotId;
            
        } catch (error) {
            console.error('Failed to import save:', error);
            throw error;
        }
    }

    /**
     * Validate save data structure
     */
    validateSaveData(saveData) {
        if (!saveData || typeof saveData !== 'object') {
            return false;
        }
        
        // Check required fields
        const requiredFields = ['id', 'timestamp', 'version', 'gameData'];
        for (const field of requiredFields) {
            if (!(field in saveData)) {
                console.error(`Missing required field: ${field}`);
                return false;
            }
        }
        
        // Check timestamp is valid
        if (!Number.isInteger(saveData.timestamp) || saveData.timestamp <= 0) {
            console.error('Invalid timestamp');
            return false;
        }
        
        // Check version format
        if (typeof saveData.version !== 'string' || !saveData.version.match(/^\d+\.\d+\.\d+$/)) {
            console.error('Invalid version format');
            return false;
        }
        
        return true;
    }

    /**
     * Persist save slots to localStorage
     */
    async persistSaveSlots() {
        try {
            const saveData = {
                version: CONFIG.game.version,
                lastUpdated: Date.now(),
                saveSlots: Object.fromEntries(this.saveSlots)
            };
            
            const dataStr = JSON.stringify(saveData);
            
            // Check localStorage quota
            if (dataStr.length > 5 * 1024 * 1024) { // 5MB limit
                console.warn('Save data approaching localStorage limit');
                
                // Clean up old saves if needed
                await this.cleanupOldSaves();
            }
            
            localStorage.setItem(this.storageKey, dataStr);
            
        } catch (error) {
            if (error.name === 'QuotaExceededError') {
                console.error('localStorage quota exceeded');
                await this.cleanupOldSaves();
                // Retry save
                localStorage.setItem(this.storageKey, JSON.stringify({
                    version: CONFIG.game.version,
                    lastUpdated: Date.now(),
                    saveSlots: Object.fromEntries(this.saveSlots)
                }));
            } else {
                throw error;
            }
        }
    }

    /**
     * Clean up old saves to free space
     */
    async cleanupOldSaves() {
        const saves = this.getSaveSlots();
        const manualSaves = saves.filter(save => save.metadata?.isManual && !save.isAutoSave);
        
        // Keep only the newest manual saves
        if (manualSaves.length > this.maxSaveSlots) {
            const toDelete = manualSaves.slice(this.maxSaveSlots);
            for (const save of toDelete) {
                this.saveSlots.delete(save.id);
            }
            console.log(`🧹 Cleaned up ${toDelete.length} old saves`);
        }
    }

    /**
     * Compress data (placeholder - would use actual compression library)
     */
    async compressData(data) {
        // In a real implementation, you'd use a compression library like pako
        // For now, just return the data as-is
        return data;
    }

    /**
     * Decompress data (placeholder)
     */
    async decompressData(data) {
        // In a real implementation, you'd decompress using the same library
        return data;
    }

    /**
     * Encrypt data (placeholder - would use actual encryption)
     */
    async encryptData(data) {
        // In a real implementation, you'd use Web Crypto API or similar
        // For now, just return the data as-is
        return data;
    }

    /**
     * Decrypt data (placeholder)
     */
    async decryptData(data) {
        // In a real implementation, you'd decrypt using the same method
        return data;
    }

    /**
     * Get storage usage information
     */
    getStorageInfo() {
        try {
            const dataStr = localStorage.getItem(this.storageKey) || '{}';
            const sizeInBytes = new Blob([dataStr]).size;
            const sizeInKB = Math.round(sizeInBytes / 1024);
            const sizeInMB = Math.round(sizeInKB / 1024 * 100) / 100;
            
            return {
                totalSlots: this.saveSlots.size,
                sizeInBytes: sizeInBytes,
                sizeInKB: sizeInKB,
                sizeInMB: sizeInMB,
                maxSlots: this.maxSaveSlots
            };
        } catch (error) {
            console.error('Failed to get storage info:', error);
            return null;
        }
    }

    /**
     * Clear all save data
     */
    async clearAllSaves() {
        this.saveSlots.clear();
        localStorage.removeItem(this.storageKey);
        console.log('💾 All save data cleared');
    }

    /**
     * Check if autosave exists
     */
    hasAutoSave() {
        return this.saveSlots.has(this.autoSaveSlot);
    }

    /**
     * Get the most recent save
     */
    getMostRecentSave() {
        const saves = this.getSaveSlots();
        return saves.length > 0 ? saves[0] : null;
    }
}
