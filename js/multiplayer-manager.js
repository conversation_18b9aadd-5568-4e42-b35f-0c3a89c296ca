/**
 * AGARTHA RPG - MULTIPLAYER MANAGER
 * Handles server-based multiplayer functionality where one player hosts AI
 * and all players share the same game state through server updates
 */

export class MultiplayerManager {
    constructor(gameEngine, uiManager) {
        this.gameEngine = gameEngine;
        this.uiManager = uiManager;
        
        // Connection state
        this.isHost = false;
        this.isConnected = false;
        this.playerId = this.generatePlayerId();
        this.gameRoomId = null;
        this.serverUrl = null;
        
        // Player management
        this.connectedPlayers = new Map();
        this.hostPlayerId = null;
        
        // WebSocket connection
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 2000;
        
        // Message queue for offline actions
        this.messageQueue = [];
        this.isProcessingQueue = false;
        
        // Bind methods
        this.handleWebSocketMessage = this.handleWebSocketMessage.bind(this);
        this.handleWebSocketClose = this.handleWebSocketClose.bind(this);
        this.handleWebSocketError = this.handleWebSocketError.bind(this);
        this.handleWebSocketOpen = this.handleWebSocketOpen.bind(this);
    }

    /**
     * Generate unique player ID
     */
    generatePlayerId() {
        return 'player_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }

    /**
     * Generate unique room ID
     */
    generateRoomId() {
        return 'room_' + Math.random().toString(36).substr(2, 9).toUpperCase();
    }

    /**
     * Host a new multiplayer game
     */
    async hostGame(serverUrl, playerData, aiModel) {
        try {
            this.serverUrl = serverUrl;
            this.gameRoomId = this.generateRoomId();
            this.isHost = true;
            this.hostPlayerId = this.playerId;
            
            console.log(`🎮 Hosting new game room: ${this.gameRoomId}`);
            
            // Connect to server
            await this.connectToServer();
            
            // Send host initialization
            this.sendMessage({
                type: 'HOST_GAME',
                roomId: this.gameRoomId,
                playerId: this.playerId,
                playerData: playerData,
                aiModel: aiModel,
                gameState: this.gameEngine.getGameState()
            });
            
            // Set up host-specific event listeners
            this.setupHostEventListeners();
            
            return {
                success: true,
                roomId: this.gameRoomId,
                shareUrl: `${window.location.origin}${window.location.pathname}?room=${this.gameRoomId}&server=${encodeURIComponent(serverUrl)}`
            };
            
        } catch (error) {
            console.error('Failed to host game:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Join an existing multiplayer game
     */
    async joinGame(serverUrl, roomId, playerData) {
        try {
            this.serverUrl = serverUrl;
            this.gameRoomId = roomId;
            this.isHost = false;
            
            console.log(`🎮 Joining game room: ${roomId}`);
            
            // Connect to server
            await this.connectToServer();
            
            // Send join request
            this.sendMessage({
                type: 'JOIN_GAME',
                roomId: roomId,
                playerId: this.playerId,
                playerData: playerData
            });
            
            return { success: true };
            
        } catch (error) {
            console.error('Failed to join game:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Connect to WebSocket server
     */
    async connectToServer() {
        return new Promise((resolve, reject) => {
            try {
                // Convert HTTP URL to WebSocket URL if needed
                let wsUrl = this.serverUrl;
                if (wsUrl.startsWith('http://')) {
                    wsUrl = wsUrl.replace('http://', 'ws://');
                } else if (wsUrl.startsWith('https://')) {
                    wsUrl = wsUrl.replace('https://', 'wss://');
                }
                
                // Add WebSocket endpoint
                if (!wsUrl.endsWith('/')) wsUrl += '/';
                wsUrl += 'ws';
                
                console.log(`🔌 Connecting to WebSocket: ${wsUrl}`);
                
                this.ws = new WebSocket(wsUrl);
                
                this.ws.onopen = (event) => {
                    this.handleWebSocketOpen(event);
                    resolve();
                };
                
                this.ws.onmessage = this.handleWebSocketMessage;
                this.ws.onclose = this.handleWebSocketClose;
                this.ws.onerror = (error) => {
                    this.handleWebSocketError(error);
                    reject(error);
                };
                
                // Timeout after 10 seconds
                setTimeout(() => {
                    if (this.ws.readyState !== WebSocket.OPEN) {
                        reject(new Error('Connection timeout'));
                    }
                }, 10000);
                
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Handle WebSocket connection open
     */
    handleWebSocketOpen(event) {
        console.log('✅ Connected to multiplayer server');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        // Process any queued messages
        this.processMessageQueue();
        
        // Show connection status
        this.uiManager.showNotification('Connected to multiplayer server', 'success');
    }

    /**
     * Handle incoming WebSocket messages
     */
    handleWebSocketMessage(event) {
        try {
            const message = JSON.parse(event.data);
            console.log('📨 Received message:', message.type);
            
            switch (message.type) {
                case 'GAME_HOSTED':
                    this.handleGameHosted(message);
                    break;
                    
                case 'GAME_JOINED':
                    this.handleGameJoined(message);
                    break;
                    
                case 'PLAYER_JOINED':
                    this.handlePlayerJoined(message);
                    break;
                    
                case 'PLAYER_LEFT':
                    this.handlePlayerLeft(message);
                    break;
                    
                case 'GAME_STATE_UPDATE':
                    this.handleGameStateUpdate(message);
                    break;
                    
                case 'PLAYER_ACTION':
                    this.handlePlayerAction(message);
                    break;
                    
                case 'AI_RESPONSE':
                    this.handleAIResponse(message);
                    break;
                    
                case 'ERROR':
                    this.handleServerError(message);
                    break;
                    
                default:
                    console.warn('Unknown message type:', message.type);
            }
            
        } catch (error) {
            console.error('Error parsing WebSocket message:', error);
        }
    }

    /**
     * Handle WebSocket connection close
     */
    handleWebSocketClose(event) {
        console.log('🔌 WebSocket connection closed');
        this.isConnected = false;
        
        if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.attemptReconnect();
        } else {
            this.uiManager.showNotification('Disconnected from multiplayer server', 'error');
        }
    }

    /**
     * Handle WebSocket errors
     */
    handleWebSocketError(error) {
        console.error('WebSocket error:', error);
        this.uiManager.showNotification('Connection error occurred', 'error');
    }

    /**
     * Attempt to reconnect to server
     */
    async attemptReconnect() {
        this.reconnectAttempts++;
        console.log(`🔄 Attempting reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
        
        this.uiManager.showNotification(`Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`, 'warning');
        
        setTimeout(async () => {
            try {
                await this.connectToServer();
                
                // Re-join the game
                if (this.gameRoomId) {
                    this.sendMessage({
                        type: this.isHost ? 'REJOIN_HOST' : 'REJOIN_GAME',
                        roomId: this.gameRoomId,
                        playerId: this.playerId
                    });
                }
                
            } catch (error) {
                console.error('Reconnect failed:', error);
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.attemptReconnect();
                }
            }
        }, this.reconnectDelay * this.reconnectAttempts);
    }

    /**
     * Send message to server
     */
    sendMessage(message) {
        if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        } else {
            // Queue message for later
            this.messageQueue.push(message);
            console.log('📤 Message queued (not connected):', message.type);
        }
    }

    /**
     * Process queued messages
     */
    async processMessageQueue() {
        if (this.isProcessingQueue || this.messageQueue.length === 0) return;
        
        this.isProcessingQueue = true;
        
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.sendMessage(message);
            
            // Small delay between messages
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        this.isProcessingQueue = false;
    }

    /**
     * Set up host-specific event listeners
     */
    setupHostEventListeners() {
        // Listen for game state changes to broadcast to other players
        this.gameEngine.on('gameStateChanged', (data) => {
            this.broadcastGameStateUpdate(data);
        });
        
        // Listen for AI responses to broadcast
        this.gameEngine.on('aiResponse', (data) => {
            this.broadcastAIResponse(data);
        });
    }

    /**
     * Handle game hosted confirmation
     */
    handleGameHosted(message) {
        console.log('✅ Game hosted successfully');
        this.uiManager.showNotification(`Game room ${this.gameRoomId} created! Share the URL with friends.`, 'success');
        
        // Update UI with room info
        this.updateMultiplayerUI();
    }

    /**
     * Handle successful game join
     */
    handleGameJoined(message) {
        console.log('✅ Joined game successfully');
        this.hostPlayerId = message.hostPlayerId;
        
        // Update local game state with server state
        if (message.gameState) {
            this.gameEngine.loadGameState(message.gameState);
        }
        
        // Update UI
        this.updateMultiplayerUI();
        this.uiManager.showNotification('Joined multiplayer game!', 'success');
    }

    /**
     * Handle new player joining
     */
    handlePlayerJoined(message) {
        const { playerId, playerData } = message;
        this.connectedPlayers.set(playerId, playerData);
        
        console.log(`👤 Player joined: ${playerData.name}`);
        this.uiManager.showNotification(`${playerData.name} joined the game`, 'info');
        
        this.updatePlayersList();
    }

    /**
     * Handle player leaving
     */
    handlePlayerLeft(message) {
        const { playerId } = message;
        const playerData = this.connectedPlayers.get(playerId);
        
        if (playerData) {
            this.connectedPlayers.delete(playerId);
            console.log(`👤 Player left: ${playerData.name}`);
            this.uiManager.showNotification(`${playerData.name} left the game`, 'info');
            
            this.updatePlayersList();
        }
    }

    /**
     * Handle game state updates from server
     */
    handleGameStateUpdate(message) {
        if (!this.isHost) {
            // Non-host players receive game state updates
            this.gameEngine.updateGameState(message.gameState, message.events);
            console.log('🔄 Game state updated from server');
        }
    }

    /**
     * Handle player actions from other players
     */
    async handlePlayerAction(message) {
        if (this.isHost) {
            // Only host processes player actions through AI
            const { playerId, action, playerData } = message;
            
            console.log(`🎮 Processing action from ${playerData.name}: ${action}`);
            
            try {
                // Process action through game engine
                const result = await this.gameEngine.processPlayerAction(playerData, action);
                
                if (result.success) {
                    // Broadcast the AI response to all players
                    this.sendMessage({
                        type: 'AI_RESPONSE_BROADCAST',
                        roomId: this.gameRoomId,
                        playerId: playerId,
                        playerData: playerData,
                        action: action,
                        response: result.response,
                        gameState: this.gameEngine.getGameState()
                    });
                }
                
            } catch (error) {
                console.error('Error processing player action:', error);
            }
        }
    }

    /**
     * Handle AI responses from host
     */
    handleAIResponse(message) {
        if (!this.isHost) {
            // Display the AI response and update game state
            const { playerData, action, response, gameState } = message;
            
            // Add messages to chat
            this.uiManager.addMessage('player', playerData.name, action);
            this.uiManager.addMessage('dm', 'The Eternal Keeper', response);
            
            // Update game state
            this.gameEngine.updateGameState(gameState);
            
            console.log(`🤖 AI response received for ${playerData.name}`);
        }
    }

    /**
     * Handle server errors
     */
    handleServerError(message) {
        console.error('Server error:', message.error);
        this.uiManager.showNotification(`Server error: ${message.error}`, 'error');
    }

    /**
     * Send player action to server
     */
    sendPlayerAction(action, playerData) {
        this.sendMessage({
            type: 'PLAYER_ACTION',
            roomId: this.gameRoomId,
            playerId: this.playerId,
            action: action,
            playerData: playerData
        });
    }

    /**
     * Broadcast game state update (host only)
     */
    broadcastGameStateUpdate(data) {
        if (this.isHost) {
            this.sendMessage({
                type: 'GAME_STATE_UPDATE_BROADCAST',
                roomId: this.gameRoomId,
                gameState: data.gameState,
                events: data.events
            });
        }
    }

    /**
     * Broadcast AI response (host only)
     */
    broadcastAIResponse(data) {
        if (this.isHost) {
            this.sendMessage({
                type: 'AI_RESPONSE_BROADCAST',
                roomId: this.gameRoomId,
                ...data
            });
        }
    }

    /**
     * Update multiplayer UI elements
     */
    updateMultiplayerUI() {
        // Update room info display
        const roomInfoElement = document.getElementById('multiplayerRoomInfo');
        if (roomInfoElement) {
            roomInfoElement.innerHTML = `
                <div class="room-info">
                    <h4>🎮 Room: ${this.gameRoomId}</h4>
                    <p>Role: ${this.isHost ? '👑 Host (AI Runner)' : '👤 Player'}</p>
                    <p>Players: ${this.connectedPlayers.size + 1}</p>
                </div>
            `;
        }
        
        this.updatePlayersList();
    }

    /**
     * Update players list display
     */
    updatePlayersList() {
        const playersListElement = document.getElementById('multiplayerPlayersList');
        if (playersListElement) {
            let playersHTML = '<h4>👥 Connected Players</h4>';
            
            // Add host
            if (this.hostPlayerId) {
                const isCurrentPlayerHost = this.hostPlayerId === this.playerId;
                playersHTML += `<div class="player-item ${isCurrentPlayerHost ? 'current-player' : ''}">
                    👑 Host ${isCurrentPlayerHost ? '(You)' : ''}
                </div>`;
            }
            
            // Add other players
            this.connectedPlayers.forEach((playerData, playerId) => {
                const isCurrentPlayer = playerId === this.playerId;
                playersHTML += `<div class="player-item ${isCurrentPlayer ? 'current-player' : ''}">
                    👤 ${playerData.name} ${isCurrentPlayer ? '(You)' : ''}
                </div>`;
            });
            
            playersListElement.innerHTML = playersHTML;
        }
    }

    /**
     * Disconnect from multiplayer session
     */
    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        
        this.isConnected = false;
        this.isHost = false;
        this.gameRoomId = null;
        this.connectedPlayers.clear();
        
        console.log('🔌 Disconnected from multiplayer session');
    }

    /**
     * Get connection status
     */
    getStatus() {
        return {
            isConnected: this.isConnected,
            isHost: this.isHost,
            roomId: this.gameRoomId,
            playerId: this.playerId,
            connectedPlayers: Array.from(this.connectedPlayers.values())
        };
    }
}
