/**
 * AGARTHA RPG - MAIN APPLICATION
 * Entry point and application orchestration
 */

console.log('🚀 Loading Agartha main.js...');

import { CONFIG, validateConfig, getModelConfig, getCharacterClass } from './config.js';
import { LOR<PERSON>, getContextualLore, getCharacterLore } from './lore.js';
import { GameEngine } from './game-engine.js';
import { UIManager } from './ui-manager.js';
import { AIManager } from './ai-manager.js';
import { CharacterManager } from './character-manager.js';
import { SaveManager } from './save-manager.js';

console.log('✅ All modules imported successfully');
console.log('📋 CONFIG loaded:', CONFIG ? 'YES' : 'NO');
console.log('📋 CONFIG.models:', CONFIG?.models ? Object.keys(CONFIG.models) : 'NONE');

/**
 * Main Application Class
 * Coordinates all game systems and manages application lifecycle
 */
class AgarthaApp {
    constructor() {
        this.gameEngine = null;
        this.uiManager = null;
        this.aiManager = null;
        this.characterManager = null;
        this.saveManager = null;

        this.initialized = false;
        this.currentScreen = 'model-setup';
        this.selectedModel = null;
        this.currentPlayer = null;
        
        // Bind methods
        this.handleModelSelection = this.handleModelSelection.bind(this);
        this.handleAIInitialization = this.handleAIInitialization.bind(this);
        this.handleCharacterCreation = this.handleCharacterCreation.bind(this);
        this.handleGameStart = this.handleGameStart.bind(this);
    }

    /**
     * Initialize the application
     */
    async initialize() {
        try {
            console.log('🔮 Initializing Agartha RPG...');
            console.log('Current screen elements:', {
                modelSetup: document.getElementById('modelSetup'),
                characterCreation: document.getElementById('characterCreation'),
                gameContainer: document.getElementById('gameContainer')
            });

            // Validate configuration
            if (!validateConfig()) {
                throw new Error('Configuration validation failed');
            }
            
            // Initialize managers
            this.uiManager = new UIManager();
            this.aiManager = new AIManager();
            this.characterManager = new CharacterManager();
            this.saveManager = new SaveManager();
            this.gameEngine = new GameEngine(this.aiManager, this.characterManager, this.saveManager);
            
            // Initialize UI
            await this.uiManager.initialize();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Verify model access first
            console.log('🔍 Verifying WebLLM model access...');
            await this.aiManager.verifyModelAccess();

            // Populate model options
            console.log('🎯 About to populate model options...');
            this.populateModelOptions();
            console.log('✅ Model options populated');

            // Check for saved game
            await this.checkForSavedGame();

            // Show initial screen
            this.showScreen('modelSetup');
            console.log('🖥️ Model setup screen shown');

            this.initialized = true;
            console.log('✅ Agartha RPG initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize Agartha RPG:', error);
            this.showError('Failed to initialize the game. Please refresh and try again.');
        }
    }

    /**
     * Set up event listeners for the application
     */
    setupEventListeners() {
        // Model selection
        document.addEventListener('click', (e) => {
            if (e.target.closest('.model-option')) {
                this.handleModelSelection(e.target.closest('.model-option'));
            }
        });
        
        // AI initialization
        const startBtn = document.getElementById('startBtn');
        if (startBtn) {
            startBtn.addEventListener('click', this.handleAIInitialization.bind(this));
        }
        
        // Character creation
        const enterGameBtn = document.getElementById('enterGameBtn');
        if (enterGameBtn) {
            enterGameBtn.addEventListener('click', this.handleCharacterCreation.bind(this));
        }
        
        // Character class selection
        const characterClass = document.getElementById('characterClass');
        if (characterClass) {
            characterClass.addEventListener('change', this.updateClassDescription.bind(this));
        }
        
        // Character form changes
        const playerName = document.getElementById('playerName');
        const characterDesc = document.getElementById('characterDesc');
        
        if (playerName) {
            playerName.addEventListener('input', this.updateCharacterPreview.bind(this));
        }
        
        if (characterDesc) {
            characterDesc.addEventListener('input', this.updateCharacterPreview.bind(this));
        }
        
        // Game controls
        const sendBtn = document.getElementById('sendBtn');
        const messageInput = document.getElementById('messageInput');
        
        if (sendBtn) {
            sendBtn.addEventListener('click', this.handleSendMessage.bind(this));
        }
        
        if (messageInput) {
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.handleSendMessage();
                }
            });
            
            messageInput.addEventListener('input', this.updateCharacterCount.bind(this));
        }
        
        // Quick actions
        document.addEventListener('click', (e) => {
            if (e.target.closest('.quick-action')) {
                const action = e.target.closest('.quick-action').dataset.action;
                this.handleQuickAction(action);
            }
        });
        
        // Modal controls
        const modalClose = document.getElementById('modalClose');
        const modalOverlay = document.getElementById('modalOverlay');
        
        if (modalClose) {
            modalClose.addEventListener('click', () => this.uiManager.hideModal());
        }
        
        if (modalOverlay) {
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) {
                    this.uiManager.hideModal();
                }
            });
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
        
        // Window events
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
        window.addEventListener('resize', this.handleResize.bind(this));
    }

    /**
     * Populate model selection options
     */
    populateModelOptions() {
        console.log('🔧 populateModelOptions called');
        console.log('📋 CONFIG:', CONFIG);
        console.log('📋 CONFIG.models:', CONFIG.models);

        const container = document.getElementById('modelOptions');
        if (!container) {
            console.error('❌ Model options container not found');
            return;
        }

        container.innerHTML = '';

        Object.entries(CONFIG.models).forEach(([modelId, config]) => {
            const option = document.createElement('div');
            option.className = 'model-option hover-lift';
            option.dataset.model = modelId;

            option.innerHTML = `
                <h4>${config.displayName}</h4>
                <p>${config.description}</p>
                <div class="model-specs">
                    <span>📦 ${config.size}</span>
                    <span>🧠 ${config.params}</span>
                    <span>⚡ ${config.speed}</span>
                </div>
                ${config.recommended ? '<div class="badge badge-success mt-sm">Recommended</div>' : ''}
            `;

            container.appendChild(option);
        });

        console.log(`✅ Populated ${Object.keys(CONFIG.models).length} model options`);
    }

    /**
     * Populate character class options
     */
    populateCharacterClasses() {
        const select = document.getElementById('characterClass');
        if (!select) {
            console.error('Character class select element not found');
            return;
        }

        select.innerHTML = '<option value="">Choose your sacred path...</option>';

        Object.entries(CONFIG.characterClasses).forEach(([className, config]) => {
            const option = document.createElement('option');
            option.value = className;
            option.textContent = `${config.name} - ${config.title}`;
            select.appendChild(option);
        });

        console.log(`✅ Populated ${Object.keys(CONFIG.characterClasses).length} character classes`);
    }

    /**
     * Handle model selection
     */
    handleModelSelection(element) {
        // Remove previous selection
        document.querySelectorAll('.model-option').forEach(el => {
            el.classList.remove('selected');
        });
        
        // Select new model
        element.classList.add('selected');
        this.selectedModel = element.dataset.model;
        
        // Update UI
        const statusText = document.getElementById('statusText');
        const startBtn = document.getElementById('startBtn');
        const statusIndicator = document.getElementById('statusIndicator');
        
        if (statusText) {
            const config = getModelConfig(this.selectedModel);
            statusText.textContent = `Selected: ${config.name} - Ready to initialize`;
        }
        
        if (startBtn) {
            startBtn.disabled = false;
        }
        
        if (statusIndicator) {
            statusIndicator.className = 'status-indicator online';
        }
    }

    /**
     * Handle AI initialization
     */
    async handleAIInitialization() {
        if (!this.selectedModel) {
            this.uiManager.showNotification('Please select an AI model first.', 'warning');
            return;
        }

        try {
            const startBtn = document.getElementById('startBtn');
            const statusDiv = document.getElementById('modelStatus');
            
            if (startBtn) startBtn.disabled = true;
            if (statusDiv) statusDiv.classList.add('loading');
            
            // Initialize AI
            const success = await this.aiManager.initialize(this.selectedModel, {
                onProgress: this.updateInitializationProgress.bind(this)
            });
            
            if (success) {
                if (statusDiv) statusDiv.classList.remove('loading');
                if (statusDiv) statusDiv.classList.add('ready');
                
                this.uiManager.showNotification('AI Dungeon Master initialized successfully!', 'success');
                
                // Transition to character creation
                setTimeout(() => {
                    console.log('🎭 Transitioning to character creation');
                    this.showScreen('characterCreation');
                    this.populateCharacterClasses();
                }, 1500);
            } else {
                throw new Error('AI initialization failed');
            }
            
        } catch (error) {
            console.error('AI initialization error:', error);
            this.uiManager.showNotification('AI initialization failed. Proceeding without AI for now.', 'warning');

            // Allow proceeding to character creation even if AI fails
            setTimeout(() => {
                console.log('🎭 Proceeding to character creation despite AI failure');
                this.showScreen('characterCreation');
                this.populateCharacterClasses();
            }, 1000);

            const startBtn = document.getElementById('startBtn');
            const statusDiv = document.getElementById('modelStatus');

            if (startBtn) startBtn.disabled = false;
            if (statusDiv) statusDiv.classList.remove('loading');
        }
    }

    /**
     * Update initialization progress
     */
    updateInitializationProgress(progress) {
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');
        const statusText = document.getElementById('statusText');
        
        if (progressBar) progressBar.style.display = 'block';
        
        if (progressFill) {
            const percent = Math.round(progress.progress * 100);
            progressFill.style.width = percent + '%';
            progressFill.textContent = percent + '%';
        }
        
        if (statusText && progress.text) {
            statusText.textContent = progress.text;
        }
    }

    /**
     * Show a specific screen
     */
    showScreen(screenName) {
        console.log(`🖥️ Switching to screen: ${screenName}`);

        // Hide all screens
        const screens = ['modelSetup', 'characterCreation', 'gameContainer'];
        screens.forEach(screen => {
            const element = document.getElementById(screen);
            if (element) {
                element.classList.remove('active');
                element.classList.add('hidden');
                element.style.display = 'none';
                console.log(`Hidden screen: ${screen}`);
            }
        });

        // Show target screen
        const targetElement = document.getElementById(screenName);
        if (targetElement) {
            targetElement.classList.remove('hidden');
            targetElement.classList.add('active');
            targetElement.style.display = screenName === 'gameContainer' ? 'flex' : 'flex';
            console.log(`Shown screen: ${screenName}`);
        } else {
            console.error(`Screen element not found: ${screenName}`);
        }

        this.currentScreen = screenName;
    }

    /**
     * Show error message
     */
    showError(message) {
        this.uiManager.showModal('Error', `
            <div class="text-center">
                <div class="text-error mb-md">❌ ${message}</div>
                <button class="btn btn-primary" onclick="location.reload()">Reload Game</button>
            </div>
        `);
    }

    /**
     * Check for saved game
     */
    async checkForSavedGame() {
        const savedGame = await this.saveManager.loadGame();
        if (savedGame) {
            console.log('Found saved game from', new Date(savedGame.timestamp).toLocaleString());
            // TODO: Implement saved game restoration
        }
    }

    /**
     * Update class description when character class changes
     */
    updateClassDescription() {
        const characterClass = document.getElementById('characterClass');
        const classDescription = document.getElementById('classDescription');

        if (!characterClass || !classDescription) return;

        const selectedClass = characterClass.value;
        if (!selectedClass) {
            classDescription.innerHTML = '<p class="text-medium">Select a class to see its description and abilities</p>';
            return;
        }

        const classConfig = getCharacterClass(selectedClass);
        if (!classConfig) return;

        classDescription.classList.add('active');
        classDescription.innerHTML = `
            <h4 class="text-primary mb-sm">${classConfig.name}</h4>
            <p class="text-medium mb-md">${classConfig.description}</p>

            <div class="mb-md">
                <h5 class="text-warning mb-sm">Primary Stat: ${classConfig.primaryStat}</h5>
                <div class="stats-preview">
                    ${Object.entries(classConfig.baseStats).map(([stat, value]) =>
                        `<span class="badge badge-secondary">${stat}: ${value}</span>`
                    ).join(' ')}
                </div>
            </div>

            <div class="mb-md">
                <h5 class="text-success mb-sm">Starting Abilities:</h5>
                <div class="abilities-preview">
                    ${classConfig.abilities.map(ability =>
                        `<span class="badge badge-success">${ability}</span>`
                    ).join(' ')}
                </div>
            </div>

            <div class="mb-md">
                <h5 class="text-warning mb-sm">Starting Items:</h5>
                <div class="items-preview">
                    ${classConfig.startingItems.map(item =>
                        `<span class="badge badge-warning">${item}</span>`
                    ).join(' ')}
                </div>
            </div>

            <div class="lore-preview">
                <h5 class="text-dim mb-sm">Lore:</h5>
                <p class="text-dim" style="font-style: italic; font-size: 0.9em;">${classConfig.lore}</p>
            </div>
        `;

        this.updateCharacterPreview();
    }

    /**
     * Update character preview
     */
    updateCharacterPreview() {
        const playerName = document.getElementById('playerName');
        const characterClass = document.getElementById('characterClass');
        const characterDesc = document.getElementById('characterDesc');
        const characterPreview = document.getElementById('characterPreview');

        if (!playerName || !characterClass || !characterPreview) return;

        const name = playerName.value.trim();
        const selectedClass = characterClass.value;
        const description = characterDesc.value.trim();

        if (!name || !selectedClass) {
            characterPreview.style.display = 'none';
            return;
        }

        const classConfig = getCharacterClass(selectedClass);
        if (!classConfig) return;

        characterPreview.style.display = 'block';

        const previewContent = characterPreview.querySelector('.preview-content');
        previewContent.innerHTML = `
            <div class="character-preview-header mb-md">
                <h4 class="text-primary">${name}</h4>
                <p class="text-medium">${selectedClass}</p>
                <p class="text-dim">${description || classConfig.description}</p>
            </div>

            <div class="preview-stats">
                ${Object.entries(classConfig.baseStats).map(([stat, value]) => `
                    <div class="preview-stat">
                        <span class="preview-stat-label">${stat}</span>
                        <span class="preview-stat-value">${value}</span>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Handle character creation
     */
    async handleCharacterCreation() {
        console.log('🎭 Character creation started');

        const playerName = document.getElementById('playerName');
        const characterClass = document.getElementById('characterClass');
        const characterDesc = document.getElementById('characterDesc');

        console.log('Form elements:', { playerName, characterClass, characterDesc });

        if (!playerName || !characterClass) {
            console.error('Missing form elements');
            return;
        }

        const name = playerName.value.trim();
        const selectedClass = characterClass.value;
        const description = characterDesc.value.trim();

        console.log('Form values:', { name, selectedClass, description });

        if (!name) {
            this.uiManager.showNotification('Please enter a character name.', 'warning');
            playerName.focus();
            return;
        }

        if (!selectedClass) {
            this.uiManager.showNotification('Please select a character class.', 'warning');
            characterClass.focus();
            return;
        }

        try {
            // Create character
            this.currentPlayer = this.characterManager.createCharacter(name, selectedClass, description);
            this.characterManager.setActiveCharacter(this.currentPlayer);

            // Initialize game state
            this.gameEngine.gameState.players.set(this.currentPlayer.id, this.currentPlayer);

            // Start the game
            this.handleGameStart();

        } catch (error) {
            console.error('Character creation error:', error);
            this.uiManager.showNotification('Failed to create character. Please try again.', 'error');
        }
    }

    /**
     * Handle game start
     */
    async handleGameStart() {
        try {
            // Show game screen
            this.showScreen('gameContainer');

            // Update UI with character info
            this.uiManager.updateCharacterInfo(this.currentPlayer);
            this.uiManager.updateInventory(this.currentPlayer);
            this.uiManager.updateQuests(this.gameEngine.gameState);
            this.uiManager.updateLocation(this.gameEngine.gameState.location, {
                chapter: this.gameEngine.gameState.chapter,
                danger: 'Safe'
            });

            // Update AI status
            this.uiManager.updateAIStatus(this.aiManager.getStatus());

            // Set up game engine event listeners
            this.setupGameEngineEvents();

            // Add welcome message
            setTimeout(() => {
                const welcomeMessage = `Welcome, ${this.currentPlayer.name} the ${this.currentPlayer.class}. You stand before the Crystal Gates of Shambhala, where amethyst pillars reach toward infinity. The gates hum with recognition as your ${this.getClassAttribute(this.currentPlayer.class)} resonates with their ancient power. What is your first action in this realm beneath realms?`;

                this.uiManager.addMessage('dm', 'The Eternal Keeper', welcomeMessage);
            }, 1000);

            // Focus on input
            const messageInput = document.getElementById('messageInput');
            if (messageInput) {
                messageInput.focus();
            }

        } catch (error) {
            console.error('Game start error:', error);
            this.uiManager.showNotification('Failed to start game. Please try again.', 'error');
        }
    }

    /**
     * Setup game engine event listeners
     */
    setupGameEngineEvents() {
        this.gameEngine.on('gameStateChanged', (data) => {
            this.uiManager.updateCharacterInfo(this.currentPlayer);
            this.uiManager.updateInventory(this.currentPlayer);
            this.uiManager.updateQuests(this.gameEngine.gameState);
        });

        this.gameEngine.on('locationChanged', (data) => {
            this.uiManager.updateLocation(data.to, {
                chapter: this.gameEngine.gameState.chapter,
                danger: this.calculateLocationDanger(data.to)
            });
        });

        this.gameEngine.on('itemAdded', (data) => {
            this.uiManager.showNotification(`Found: ${data.item}`, 'success');
        });

        this.gameEngine.on('questCompleted', (data) => {
            this.uiManager.showNotification(`Quest completed: ${data.quest.title}`, 'success');

            if (data.isEndGame) {
                this.handleGameCompletion(data);
            }
        });

        this.gameEngine.on('questActivated', (data) => {
            this.uiManager.showNotification(`New quest available: ${data.quest.title}`, 'info');
        });

        this.gameEngine.on('gameCompleted', (data) => {
            this.handleGameCompletion(data);
        });

        this.gameEngine.on('randomEvent', (data) => {
            this.uiManager.addMessage('environment', 'The Inner Realm', data.event);
        });
    }

    /**
     * Handle sending messages
     */
    async handleSendMessage() {
        const messageInput = document.getElementById('messageInput');
        if (!messageInput || !this.currentPlayer) return;

        const message = messageInput.value.trim();
        if (!message) return;

        if (this.gameEngine.isProcessing) {
            this.uiManager.showNotification('Please wait for the current action to complete.', 'warning');
            return;
        }

        try {
            // Clear input
            messageInput.value = '';
            this.updateCharacterCount(messageInput);

            // Show typing indicator
            this.uiManager.showTypingIndicator();

            // Add player message
            this.uiManager.addMessage('player', this.currentPlayer.name, message);

            // Process action through game engine
            const result = await this.gameEngine.processPlayerAction(this.currentPlayer, message);

            // Hide typing indicator
            this.uiManager.hideTypingIndicator();

            if (result.success) {
                // Add DM response
                this.uiManager.addMessage('dm', 'The Eternal Keeper', result.response);
            } else {
                this.uiManager.showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('Message send error:', error);
            this.uiManager.hideTypingIndicator();
            this.uiManager.showNotification('Failed to process action. Please try again.', 'error');
        }

        // Focus back on input
        messageInput.focus();
    }

    /**
     * Handle quick actions
     */
    handleQuickAction(action) {
        const messageInput = document.getElementById('messageInput');
        if (messageInput) {
            messageInput.value = action;
            messageInput.focus();
        }
    }

    /**
     * Update character count display
     */
    updateCharacterCount(input) {
        this.uiManager.updateCharacterCount(input);
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(e) {
        // Escape key - close modals
        if (e.key === 'Escape') {
            this.uiManager.hideModal();
        }

        // Ctrl/Cmd + S - Save game
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            this.handleSaveGame();
        }

        // Ctrl/Cmd + L - Load game
        if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
            e.preventDefault();
            this.handleLoadGame();
        }
    }

    /**
     * Handle save game
     */
    async handleSaveGame() {
        if (!this.currentPlayer) return;

        try {
            await this.gameEngine.saveGame();
            this.uiManager.showNotification('Game saved successfully!', 'success');
        } catch (error) {
            console.error('Save error:', error);
            this.uiManager.showNotification('Failed to save game.', 'error');
        }
    }

    /**
     * Handle load game
     */
    async handleLoadGame() {
        // TODO: Implement load game UI
        this.uiManager.showNotification('Load game feature coming soon!', 'info');
    }

    /**
     * Handle before unload
     */
    handleBeforeUnload(e) {
        if (this.currentPlayer && this.gameEngine) {
            // Auto-save before leaving
            this.gameEngine.autoSave();
        }
    }

    /**
     * Handle window resize
     */
    handleResize() {
        if (this.uiManager) {
            this.uiManager.handleResize();
        }
    }

    /**
     * Get class attribute for flavor text
     */
    getClassAttribute(characterClass) {
        const attributes = {
            'Crystal Keeper': 'crystalline aura',
            'Vril Engineer': 'energy field',
            'Lemurian Scholar': 'ancient wisdom',
            'Atlantean Warrior': 'warrior spirit',
            'Inner Earth Scout': 'heightened senses',
            'Light Weaver': 'luminous essence'
        };

        return attributes[characterClass] || 'inner light';
    }

    /**
     * Handle game completion
     */
    handleGameCompletion(data) {
        const playTimeHours = Math.round((data.playTime / (1000 * 60 * 60)) * 10) / 10;
        const completionDate = new Date(data.completionTime).toLocaleDateString();

        this.uiManager.showModal('🎉 Congratulations, Ascended Master!', `
            <div class="text-center">
                <div class="mb-lg">
                    <h2 class="text-primary mb-md">You have completed the Great Work!</h2>
                    <p class="text-medium mb-md">
                        ${data.player.name} the ${data.player.class} has successfully united the surface and inner worlds,
                        ushering in the Golden Age of human consciousness.
                    </p>
                </div>

                <div class="completion-stats glass p-lg mb-lg">
                    <h3 class="text-warning mb-md">Final Statistics</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">Final Score</span>
                            <span class="stat-value text-primary">${data.finalScore?.toLocaleString() || 'N/A'}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Character Level</span>
                            <span class="stat-value text-success">${data.player.level}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Play Time</span>
                            <span class="stat-value text-warning">${playTimeHours} hours</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Completion Date</span>
                            <span class="stat-value text-info">${completionDate}</span>
                        </div>
                    </div>
                </div>

                <div class="achievements mb-lg">
                    <h3 class="text-success mb-md">🏆 Achievements Unlocked</h3>
                    <div class="achievement-list">
                        <div class="badge badge-success mb-sm">🌟 Master of Agartha</div>
                        <div class="badge badge-success mb-sm">🔮 Vril Adept</div>
                        <div class="badge badge-success mb-sm">🧠 Telepathic Awakened</div>
                        <div class="badge badge-success mb-sm">⚡ Energy Harmonizer</div>
                        <div class="badge badge-success mb-sm">🌍 World Uniter</div>
                        <div class="badge badge-success mb-sm">👑 Herald of the Golden Age</div>
                    </div>
                </div>

                <div class="ending-message glass p-md mb-lg">
                    <p class="text-medium" style="font-style: italic;">
                        "The ancient prophecies have been fulfilled. The surface and inner worlds are now united in harmony.
                        As an Ascended Master, you carry the responsibility of guiding humanity into its next evolutionary phase.
                        The Great Work is complete, but your journey as a guardian of consciousness has just begun."
                    </p>
                    <p class="text-dim mt-md">- The Eternal Keeper</p>
                </div>

                <div class="completion-actions">
                    <button class="btn btn-primary mr-md" onclick="window.agarthaApp.startNewGamePlus()">
                        🔄 New Game+
                    </button>
                    <button class="btn btn-secondary mr-md" onclick="window.agarthaApp.exportGameCompletion()">
                        📤 Export Certificate
                    </button>
                    <button class="btn btn-warning" onclick="window.agarthaApp.continueExploring()">
                        🗺️ Continue Exploring
                    </button>
                </div>
            </div>
        `);
    }

    /**
     * Start New Game Plus mode
     */
    startNewGamePlus() {
        this.uiManager.showNotification('New Game+ feature coming soon! You can continue exploring for now.', 'info');
        this.uiManager.hideModal();
    }

    /**
     * Export game completion certificate
     */
    exportGameCompletion() {
        const certificate = {
            playerName: this.currentPlayer.name,
            characterClass: this.currentPlayer.class,
            completionDate: new Date().toISOString(),
            finalScore: this.gameEngine.gameState.finalScore,
            playTime: this.gameEngine.gameState.gameStats.playTime,
            achievements: ['Master of Agartha', 'Vril Adept', 'Telepathic Awakened', 'Energy Harmonizer', 'World Uniter', 'Herald of the Golden Age']
        };

        const dataStr = JSON.stringify(certificate, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `agartha_completion_${this.currentPlayer.name}_${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        URL.revokeObjectURL(link.href);
        this.uiManager.showNotification('Completion certificate exported!', 'success');
    }

    /**
     * Continue exploring after game completion
     */
    continueExploring() {
        this.uiManager.hideModal();
        this.uiManager.showNotification('You may continue exploring the vast realms of Agartha!', 'info');

        // Add a special post-game message
        setTimeout(() => {
            const postGameMessage = `As an Ascended Master, you now perceive Agartha with new eyes. The crystalline networks pulse with deeper meaning, and you sense infinite mysteries yet to be explored. The realm responds to your elevated consciousness, revealing hidden chambers and ancient secrets previously beyond mortal comprehension.`;
            this.uiManager.addMessage('dm', 'The Eternal Keeper', postGameMessage);
        }, 2000);
    }

    /**
     * Calculate location danger level
     */
    calculateLocationDanger(locationName) {
        const dangerLevels = {
            'Crystal Gates of Shambhala': 'Safe',
            'Shambhala Central Plaza': 'Safe',
            'Hall of Records': 'Low',
            'Telos Beneath Mt. Shasta': 'Low',
            'Vril Power Station': 'Medium',
            'Deep Tunnels': 'High',
            'Forbidden Chambers': 'Extreme'
        };

        return dangerLevels[locationName] || 'Medium';
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    console.log(`
╔══════════════════════════════════════════════════════╗
║     AGARTHA: AI-POWERED RPG - PRODUCTION READY      ║
╠══════════════════════════════════════════════════════╣
║  🤖 Powered by Web-LLM (runs entirely in browser)   ║
║  🎮 Full RPG mechanics with inventory & stats       ║
║  ✨ Dynamic AI dungeon master                       ║
║  🌐 Ready for deployment on any web server          ║
║  💾 Auto-save functionality                         ║
║  📱 Mobile responsive design                        ║
║  🔮 Rich lore based on real mythology               ║
╚══════════════════════════════════════════════════════╝
    `);
    
    // Create and initialize the application
    window.agarthaApp = new AgarthaApp();
    await window.agarthaApp.initialize();
});

// Export for module usage
export { AgarthaApp };
