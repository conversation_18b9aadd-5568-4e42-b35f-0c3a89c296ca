/**
 * AGARTHA RPG - UI MANAGER
 * Handles user interface updates and interactions
 */

import { CONFIG } from './config.js';

/**
 * UI Manager Class
 * Manages all user interface updates and interactions
 */
export class UIManager {
    constructor() {
        this.notifications = [];
        this.modals = [];
        this.currentModal = null;
        this.typingSpeed = CONFIG.ui.typingSpeed;
        this.soundEnabled = CONFIG.ui.soundEnabled;
        
        // Animation queues
        this.animationQueue = [];
        this.isAnimating = false;
    }

    /**
     * Initialize the UI Manager
     */
    async initialize() {
        console.log('🎨 Initializing UI Manager...');
        
        // Set up notification container
        this.createNotificationContainer();
        
        // Set up particle system
        this.initializeParticleSystem();
        
        // Set up responsive handlers
        this.setupResponsiveHandlers();
        
        console.log('✅ UI Manager initialized');
    }

    /**
     * Create notification container
     */
    createNotificationContainer() {
        if (document.getElementById('notificationContainer')) return;
        
        const container = document.createElement('div');
        container.id = 'notificationContainer';
        container.className = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 5000;
            display: flex;
            flex-direction: column;
            gap: 10px;
            pointer-events: none;
        `;
        
        document.body.appendChild(container);
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type} slide-in-right`;
        notification.style.cssText = `
            background: var(--gradient-card);
            border: var(--border-glass);
            border-radius: var(--border-radius);
            padding: var(--spacing-md) var(--spacing-lg);
            color: var(--text-light);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-medium);
            pointer-events: auto;
            cursor: pointer;
            transition: all var(--transition-medium);
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        // Set type-specific styling
        const typeStyles = {
            success: 'border-left: 4px solid var(--success-green);',
            warning: 'border-left: 4px solid var(--warning-amber);',
            error: 'border-left: 4px solid var(--error-red);',
            info: 'border-left: 4px solid var(--info-blue);'
        };
        
        notification.style.cssText += typeStyles[type] || typeStyles.info;
        
        // Add icon based on type
        const icons = {
            success: '✅',
            warning: '⚠️',
            error: '❌',
            info: 'ℹ️'
        };
        
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 1.2em;">${icons[type] || icons.info}</span>
                <span>${message}</span>
            </div>
        `;
        
        // Add click to dismiss
        notification.addEventListener('click', () => {
            this.removeNotification(notification);
        });
        
        // Add to container
        const container = document.getElementById('notificationContainer');
        container.appendChild(notification);
        
        // Auto-remove after duration
        setTimeout(() => {
            this.removeNotification(notification);
        }, duration);
        
        this.notifications.push(notification);
    }

    /**
     * Remove notification
     */
    removeNotification(notification) {
        notification.classList.add('fade-out');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            this.notifications = this.notifications.filter(n => n !== notification);
        }, 300);
    }

    /**
     * Show modal
     */
    showModal(title, content, options = {}) {
        const overlay = document.getElementById('modalOverlay');
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalContent = document.getElementById('modalContent');
        
        if (!overlay || !modal) {
            console.error('Modal elements not found');
            return;
        }
        
        modalTitle.textContent = title;
        modalContent.innerHTML = content;
        
        overlay.classList.add('active');
        modal.classList.add('scale-in');
        
        this.currentModal = { title, content, options };
        
        // Handle escape key
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                this.hideModal();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        
        document.addEventListener('keydown', handleEscape);
    }

    /**
     * Hide modal
     */
    hideModal() {
        const overlay = document.getElementById('modalOverlay');
        const modal = document.getElementById('modal');
        
        if (overlay && modal) {
            modal.classList.add('scale-out');
            setTimeout(() => {
                overlay.classList.remove('active');
                modal.classList.remove('scale-in', 'scale-out');
            }, 300);
        }
        
        this.currentModal = null;
    }

    /**
     * Update character information display
     */
    updateCharacterInfo(character) {
        const characterInfo = document.getElementById('characterInfo');
        if (!characterInfo) return;
        
        const healthPercent = (character.health / character.maxHealth) * 100;
        const vrilPercent = (character.vril / character.maxVril) * 100;
        const expPercent = character.experienceToNext > 0 ? 
            ((character.experience - this.getExperienceForLevel(character.level)) / 
             (this.getExperienceForLevel(character.level + 1) - this.getExperienceForLevel(character.level))) * 100 : 100;
        
        characterInfo.innerHTML = `
            <div class="character-header">
                <div class="character-name">${character.name}</div>
                <div class="character-class">${character.class}</div>
                <div class="character-level">
                    <span class="badge badge-primary">Level ${character.level}</span>
                    <span class="text-dim">${character.experience} XP</span>
                </div>
            </div>
            
            <div class="health-bar">
                <div class="bar-label">
                    <span class="text-error">❤️ Health</span>
                    <span class="text-error">${character.health}/${character.maxHealth}</span>
                </div>
                <div class="stat-bar">
                    <div class="stat-fill" style="width: ${healthPercent}%; background: linear-gradient(90deg, #ff6b6b, #ff4444);"></div>
                </div>
            </div>
            
            <div class="vril-bar">
                <div class="bar-label">
                    <span class="text-primary">✨ Vril Energy</span>
                    <span class="text-primary">${character.vril}/${character.maxVril}</span>
                </div>
                <div class="stat-bar">
                    <div class="stat-fill" style="width: ${vrilPercent}%; background: var(--gradient-primary);"></div>
                </div>
            </div>
            
            <div class="experience-bar mb-md">
                <div class="bar-label">
                    <span class="text-warning">⭐ Experience</span>
                    <span class="text-warning">${character.experienceToNext} to next</span>
                </div>
                <div class="stat-bar">
                    <div class="stat-fill" style="width: ${expPercent}%; background: linear-gradient(90deg, #ffd700, #ffaa00);"></div>
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-label">Vril</span>
                    <span class="stat-value">${character.stats.vril}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Wisdom</span>
                    <span class="stat-value">${character.stats.wisdom}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Resonance</span>
                    <span class="stat-value">${character.stats.resonance}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Vitality</span>
                    <span class="stat-value">${character.stats.vitality}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Agility</span>
                    <span class="stat-value">${character.stats.agility}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Strength</span>
                    <span class="stat-value">${character.stats.strength}</span>
                </div>
            </div>
        `;
    }

    /**
     * Update quest display
     */
    updateQuests(gameState) {
        const questList = document.getElementById('questList');
        const questCount = document.getElementById('questCount');

        if (!questList) return;

        const activeQuests = gameState.worldState?.questsActive || [];

        // Update count
        if (questCount) {
            questCount.textContent = activeQuests.length;
        }

        // Clear existing quests
        questList.innerHTML = '';

        if (activeQuests.length === 0) {
            questList.innerHTML = '<div class="text-dim text-center p-md">No active quests</div>';
            return;
        }

        activeQuests.forEach(quest => {
            const questDiv = document.createElement('div');
            questDiv.className = 'quest-item glass p-md mb-sm';

            const completedObjectives = quest.completedObjectives || [];
            const totalObjectives = quest.objectives?.length || 0;
            const progress = totalObjectives > 0 ? (completedObjectives.length / totalObjectives) * 100 : 0;

            questDiv.innerHTML = `
                <div class="quest-header mb-sm">
                    <h4 class="quest-title text-warning">${quest.title}</h4>
                    <span class="badge badge-secondary">Ch. ${quest.chapter || 1}</span>
                </div>
                <p class="quest-description text-dim mb-sm">${quest.description}</p>

                <div class="quest-progress mb-sm">
                    <div class="bar-label">
                        <span class="text-primary">Progress</span>
                        <span class="text-primary">${completedObjectives.length}/${totalObjectives}</span>
                    </div>
                    <div class="stat-bar">
                        <div class="stat-fill" style="width: ${progress}%; background: linear-gradient(90deg, #ffd700, #ffaa00);"></div>
                    </div>
                </div>

                <div class="quest-objectives">
                    ${quest.objectives?.map((objective, index) => {
                        const isCompleted = completedObjectives.includes(index);
                        return `
                            <div class="objective ${isCompleted ? 'completed' : 'active'}">
                                <span class="objective-icon">${isCompleted ? '✅' : '🔸'}</span>
                                <span class="objective-text ${isCompleted ? 'text-success' : 'text-light'}">${objective}</span>
                            </div>
                        `;
                    }).join('') || ''}
                </div>
            `;

            questList.appendChild(questDiv);
        });
    }

    /**
     * Update inventory display
     */
    updateInventory(character) {
        const inventory = document.getElementById('inventory');
        const inventoryCount = document.getElementById('inventoryCount');
        
        if (!inventory) return;
        
        // Clear existing slots
        inventory.innerHTML = '';
        
        // Create 12 inventory slots
        for (let i = 0; i < 12; i++) {
            const slot = document.createElement('div');
            slot.className = 'inventory-slot';
            slot.dataset.slot = i;
            
            if (character.inventory[i]) {
                const item = character.inventory[i];
                slot.classList.add('filled');
                slot.innerHTML = this.getItemIcon(item);
                slot.title = typeof item === 'string' ? item : item.name;
            } else {
                slot.classList.add('empty');
                slot.innerHTML = '—';
                slot.title = 'Empty slot';
            }
            
            inventory.appendChild(slot);
        }
        
        // Update count
        if (inventoryCount) {
            const filledSlots = character.inventory.filter(item => item).length;
            inventoryCount.textContent = `${filledSlots}/12`;
        }
    }

    /**
     * Get icon for item type
     */
    getItemIcon(item) {
        const itemName = typeof item === 'string' ? item.toLowerCase() : item.name.toLowerCase();
        
        if (itemName.includes('crystal')) return '💎';
        if (itemName.includes('scroll') || itemName.includes('tome')) return '📜';
        if (itemName.includes('key')) return '🗝️';
        if (itemName.includes('armor')) return '🛡️';
        if (itemName.includes('weapon') || itemName.includes('blade')) return '⚔️';
        if (itemName.includes('potion')) return '🧪';
        if (itemName.includes('tool')) return '🔧';
        if (itemName.includes('artifact')) return '🏺';
        
        return '✨';
    }

    /**
     * Add message to chat
     */
    addMessage(type, author, content, options = {}) {
        const chatMessages = document.getElementById('chatMessages');
        if (!chatMessages) return;
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type} slide-in-up`;
        
        const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        messageDiv.innerHTML = `
            <div class="message-author">
                ${author}
                ${type !== 'system' && type !== 'environment' ? `<span class="message-timestamp">${timestamp}</span>` : ''}
            </div>
            <div class="message-content">${this.formatMessageContent(content)}</div>
        `;
        
        chatMessages.appendChild(messageDiv);
        
        // Auto-scroll to bottom
        if (CONFIG.ui.autoScrollEnabled) {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // Remove old messages if too many
        const messages = chatMessages.querySelectorAll('.message');
        if (messages.length > CONFIG.ui.maxChatHistory) {
            messages[0].remove();
        }
        
        return messageDiv;
    }

    /**
     * Format message content with special styling
     */
    formatMessageContent(content) {
        let formatted = content;
        
        // Format dice rolls
        formatted = formatted.replace(/\[Roll: ?(\d+)\]/gi, '<span class="roll badge badge-warning">[Roll: $1]</span>');
        
        // Format locations
        formatted = formatted.replace(/\b(Crystal Gates|Shambhala|Telos|Poseid|Hall of Records|Atlantean|Lemurian|Agarthan)\b/g, 
            '<span class="location text-primary">$1</span>');
        
        // Format NPCs
        formatted = formatted.replace(/\b(Telosian|Guardian|Elder|Keeper|Zephyr|Adama|Thoth)\b/g, 
            '<span class="npc text-success">$1</span>');
        
        // Format items and artifacts
        formatted = formatted.replace(/\b(crystal|vril|artifact|scroll|key|blade|armor)\b/gi, 
            '<span class="item text-warning">$1</span>');
        
        // Format abilities and powers
        formatted = formatted.replace(/\b(telepathy|resonance|channeling|meditation|harmony)\b/gi, 
            '<span class="ability text-primary">$1</span>');
        
        return formatted;
    }

    /**
     * Update location display
     */
    updateLocation(locationName, details = {}) {
        const locationElement = document.getElementById('currentLocation');
        const chapterElement = document.getElementById('locationChapter');
        const dangerElement = document.getElementById('locationDanger');
        
        if (locationElement) {
            locationElement.textContent = locationName;
        }
        
        if (chapterElement && details.chapter) {
            chapterElement.textContent = `Chapter ${details.chapter}`;
        }
        
        if (dangerElement && details.danger) {
            dangerElement.textContent = details.danger;
            dangerElement.className = `badge badge-${this.getDangerColor(details.danger)}`;
        }
    }

    /**
     * Get color class for danger level
     */
    getDangerColor(danger) {
        const dangerColors = {
            'Safe': 'success',
            'Low': 'warning',
            'Medium': 'warning',
            'High': 'error',
            'Extreme': 'error'
        };
        
        return dangerColors[danger] || 'secondary';
    }

    /**
     * Update AI status display
     */
    updateAIStatus(status) {
        const aiIndicator = document.getElementById('aiIndicator');
        const aiModelName = document.getElementById('aiModelName');
        const aiStats = document.getElementById('aiStats');
        const aiPerformance = document.getElementById('aiPerformance');
        
        if (aiIndicator) {
            aiIndicator.className = `status-indicator ${status.isReady ? 'online' : 'offline'}`;
        }
        
        if (aiModelName && status.modelConfig) {
            aiModelName.textContent = status.modelConfig.name;
        }
        
        if (aiStats && status.modelConfig) {
            aiStats.textContent = `Context: ${status.modelConfig.contextLength} tokens`;
        }
        
        if (aiPerformance && status.performanceMetrics) {
            const avgTime = Math.round(status.performanceMetrics.averageResponseTime);
            aiPerformance.innerHTML = `<small class="text-dim">Response time: ${avgTime}ms</small>`;
        }
    }

    /**
     * Show typing indicator
     */
    showTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        if (indicator) {
            indicator.style.display = 'block';
        }
    }

    /**
     * Hide typing indicator
     */
    hideTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    /**
     * Update character count
     */
    updateCharacterCount(input) {
        const counter = document.getElementById('characterCount');
        if (counter) {
            const count = input.value.length;
            const max = input.maxLength || 500;
            counter.textContent = `${count}/${max}`;
            
            if (count > max * 0.9) {
                counter.classList.add('text-warning');
            } else {
                counter.classList.remove('text-warning');
            }
        }
    }

    /**
     * Initialize particle system
     */
    initializeParticleSystem() {
        if (!CONFIG.ui.particleEffects) return;
        
        // Create particle container
        const container = document.createElement('div');
        container.className = 'particle-container';
        container.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: -1;
        `;
        
        document.body.appendChild(container);
        
        // Generate particles periodically
        setInterval(() => {
            if (Math.random() > 0.7) {
                this.createParticle(container);
            }
        }, 2000);
    }

    /**
     * Create a floating particle
     */
    createParticle(container) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDuration = (Math.random() * 4 + 6) + 's';
        particle.style.opacity = Math.random() * 0.5 + 0.2;
        
        container.appendChild(particle);
        
        // Remove particle after animation
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 10000);
    }

    /**
     * Setup responsive handlers
     */
    setupResponsiveHandlers() {
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                this.handleResize();
            }, 250);
        });
    }

    /**
     * Handle window resize
     */
    handleResize() {
        // Adjust UI elements for different screen sizes
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            // Mobile-specific adjustments
            document.body.classList.add('mobile-layout');
        } else {
            document.body.classList.remove('mobile-layout');
        }
    }

    /**
     * Get experience for level (helper method)
     */
    getExperienceForLevel(level) {
        if (level <= 1) return 0;
        return Math.floor(100 * Math.pow(level - 1, 1.5));
    }
}
